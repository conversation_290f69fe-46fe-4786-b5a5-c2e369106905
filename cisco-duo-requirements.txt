Cisco Duo
Duo Admin API
Authentication Logs
This classification was performed based on the Reason field (explaining why the authentication attempt was allowed or
denied) and the Result field (indicating whether the attempt was successful, denied, failed, error, or marked as fraud), as
defined in the authentication logs documentation.

Reason

Description

Result

Alert Type

Justification

user_marked_fr Return events
aud
where
authentication
was denied
because the
end user
explicitly
marked
"fraudulent".

fraud

Threat

User explicitly
marked the
authentication
attempt as
fraudulent.

error

error

Observation

Authentication
attempt failed
due to an
unspecified
error.

allow_unenrolle Return events
success
d_user
where
authentication
was successful
because of the
following
policy: "allow
not enrolled
users".

Audit

Authentication
succeeded due
to policy
allowing
unenrolled
users.

allowed_by_poli Return events
success
cy
where
authentication
was successful
because of a
policy.

Audit

Authentication
allowed based
on configured
policy.

Return events
where
authentication
was denied
because of an
error.

allow_unenrolle Return events
success
d_user_on_trust where
ed_network
authentication
was successful
because the
unenrolled
user's access
device was on
an authorized
network.

Audit

Authentication
allowed for
unenrolled user
from trusted
network.

bypass_user

Return events
success
where
authentication
was successful
because a
bypass code
was used.

Control
Violation

Authentication
succeeded
using a bypass
code; high risk
access route.

remembered_d
evice

Return events
success
where
authentication
was successful
because the
end user was
on a
remembered
device.

Observation

Authentication
allowed due to
remembered
device policy.

trusted_locatio
n

Return events
success
where
authentication
was successful
because the
end user was in
a trusted
location.

Observation

Authentication
allowed from a
trusted
location.

trusted_networ
k

Return events
success
where
authentication
was successful
because the
end user was
on a trusted
network.

Observation

Authentication
allowed from a
trusted
network.

user_approved

Return events
success
where
authentication
was successful
because the
end user
approved the
authentication
request.

Audit

Authentication
approved by
the user.

valid_passcode Return events
success
where
authentication
was successful
because the
end user used
a valid
passcode.

Audit

Authentication
succeeded
using valid
passcode.

verification_cod Return events
success
e_correct
where
authentication
was successful
because of a
Verified Duo
Push.

Audit

Authentication
succeeded via
correct Verified
Duo Push code.

anonymous_ip

Threat

Authentication
attempt from
anonymous IP
address. Can
be mapped
with Count

Return events
where
authentication
was denied
because the
authentication
request came
from an
anonymous IP
address.

failure

could_not_dete Return events
rmine_if_endpoi where
nt_was_trusted authentication
was denied
because it
could not be
determined if
the endpoint
was trusted.

failure

Observation

System could
not determine
trust status of
endpoint.

denied_by_polic Return events
y
where
authentication
was denied
because of a
policy.

failure

Control
Violation

Authentication
denied due to
policy
enforcement.

deny_unenrolle
d_user

Return events
where
authentication
was denied
because of the
following
policy: "deny
not enrolled
users".

failure

Control
Violation

Authentication
denied for
unenrolled user
per policy.

endpoint_is_not Return events
_in_manageme where
nt_system
authentication
was denied
because the
endpoint is not
in a
management
system.

failure

Control
Violation

Endpoint not
registered in
management
system.

endpoint_failed Return events
_google_verific where
ation
authentication
was denied
because the
endpoint failed
Google
verification.

failure

Control
Violation

Endpoint failed
Google
attestation
check.

endpoint_is_not Return events
_trusted
where
authentication
was denied
because the
endpoint was
not trusted.

failure

Observation

Authentication
denied due to
untrusted
endpoint.

factor_restricte
d

Return events
where
authentication
was denied
because the
authentication
method used
was not
allowed.

failure

Control
Violation

Authentication
denied due to
restricted
authentication
factor.

frequent_attem
pts

Return events
where
authentication
was denied
because of
frequent
attempts.

failure

Threat

Authentication
denied due to
rapid or
repeated
attempts.

invalid_device

Return events
where
authentication
was denied
because the
device was
invalid.

failure

Observation

Authentication
denied due to
invalid device.

invalid_passco
de

Return events
where
authentication
was denied
because the
passcode was
invalid.

failure

Observation

Authentication
denied due to
invalid
passcode input.

invalid_referrin Return events
g_hostname_pr where
ovided
authentication
was denied
because an

failure

Observation

Invalid
referring
hostname used
during
authentication.

invalid referring
hostname was
provided.
location_restric Return events
ted
where
authentication
was denied
because the
end user's
location was
restricted.

failure

Control
Violation

Authentication
denied due to
geolocation
restriction.

locked_out

Return events
generated by
users that are
locked out.

failure

Audit

Authentication
denied due to
user lockout.

no_activated_d
uo_mobile_acc
ount

Return events
where
authentication
was denied
because the
end user does
not have an
activated Duo
Mobile app
account.

failure

Observation

Authentication
denied: no
activated Duo
Mobile app.

no_disk_encryp Return events
tion
where
authentication
was denied
because the
approval
device did not
have disk
encryption
enabled.

failure

Audit

Authentication
denied due to
missing disk
encryption.

no_duo_certific
ate_present

failure

Observation

Authentication
denied: missing
Duo certificate.

Return events
where
authentication
was denied
because there
was no Duo

certificate
present.
touchid_disable Return events
failure
d
where
authentication
failed because
Touch ID was
disabled on the
device.

Observation

Authentication
denied:
biometric auth
disabled.

no_referring_ho Return events
stname_provide where
d
authentication
was denied
because no
referring
hostname was
provided.

failure

Observation

Missing
referring
hostname
during
authentication.

no_response

failure

Observation

Authentication
denied due to
no response
from user.

no_screen_lock Return events
failure
where
authentication
was denied
because the
approval
device does not
have screen
lock enabled.

Audit

Authentication
denied: device
has no screen
lock.

no_web_referer Return events
failure
_match
where
authentication
was denied
because an
invalid referring
hostname did

Observation

Web referer did
not match
expected
hostnames.

Return events
where
authentication
was denied
because there
was no
response from
the user.

not match an
application's
hostnames list.
out_of_date

Return events
where
authentication
was denied
because the
software was
out of date.

failure

Audit

Authentication
denied due to
outdated
software
version.

platform_restric Return events
ted
where
authentication
was denied
because the
access
platform was
not allowed.

failure

Control
Violation

Authentication
denied due to
platform
restriction
policy.

rooted_device

Return events
where
authentication
was denied
because the
approval
device was
rooted.

failure

Audit
Could be
Threat too

Authentication
denied: device
is rooted.

software_restri
cted

Return events
where
authentication
was denied
because of
software
restriction.

failure

Observation

Authentication
denied due to
restricted
software.

user_cancelled

Return events
where
authentication
was denied
because the
end user
cancelled the
request.

failure

Observation

User cancelled
the
authentication
request.

user_disabled

Return events
where
authentication
was denied
because the
user was
disabled.

failure

Audit

Authentication
denied: user
account is
disabled.

user_mistake

Return events
where the user
performed an
action
incorrectly,
such as
entering the
wrong
password or
misusing the
interface.

failure

Observation

Authentication
denied: user
marked request
as mistake.

user_not_in_per Return events
failure
mitted_group
where
authentication
was denied
because the
user did not
belong to one
of
the Permitted
Groups specifie
d in the
application's
settings.

Control
Violation

Authentication
denied: user
not in permitted
group.

user_provided_i Return events
nvalid_certificat where
e
authentication
was denied
because an
invalid
management
certificate was
provided.

Audit

Invalid
certificate
presented
during
authentication.

failure

version_restrict Return events
failure
ed
where
authentication
was denied
because the
software
version was not
allowed.

Control
Violation

Authentication
denied due to
restricted
software
version.

verification_cod Return events
failure
e_missing
where
authentication
was denied
because the
user used an
old version of
Duo Mobile that
does not
support Verified
Duo Push.

Observation

Verification
code missing
from auth
request.

verification_cod Return events
failure
e_incorrect
where
authentication
was denied
because the
user entered
the wrong code
when
approving a
Verified Duo
Push.

Observation

Incorrect
verification
code entered
during auth.

invalid_manage
ment_certificat
e_collection_st
ate

Observation

Auth denied
due to bad
certificate
state.

Return events
failure
where
authentication
was denied
because of an
invalid
management
certificate
collection state.

queued_inflight Return events
failure
_auth_expired where
authentication
was denied
when more
authentications
than the
number
allowed by the
lockout
threshold are
started
simultaneously.
The
authentications
past the
threshold are
queued, and
then removed
from the queue
after enough
failures trigger
a lockout.

Observation

Too many
concurrent auth
attempts;
lockout applied.

Trust Monitor
Alert typing was performed based on the type of security event and the corresponding explanations field, which
provides the context for why Trust Monitor surfaced the event.

Type

Explanations

Description

Alert Type

Justification

auth

GRANTED_AUTH

Authentication was
granted despite
being flagged by
Trust Monitor

Observation

Granted
authentication,
flagged for
monitoring

auth

NEW_COUNTRY_CO
DE

Authentication
attempt from a new
country code

Observation

New geolocation
may indicate risk,
requires review

auth

NEW_DEVICE

Authentication
attempt from a new
device

Observation

New device seen,
baseline change

auth

NEW_FACTOR

Authentication using
a new factor

Observation

New authentication
method used

auth

NEW_NETBLOCK

Authentication
attempt from a new
network block

Observation

New network range
used for
authentication

auth

UNREALISTIC_GEO
VELOCITY

Improbable travel
distance/time
between
authentication
attempts

Threat
Observation (If user
travelling)

Possible account
compromise through
impossible travel

auth

UNUSUAL_COUNTR
Y_CODE

Authentication from
an unusual country

Observation

Unusual geo may
indicate risk

auth

UNUSUAL_DEVICE

Authentication from
an unusual device

Observation

Unusual device for
the user

auth

UNUSUAL_FACTOR

Authentication using
an unusual factor

Observation

Unusual auth factor
used

auth

UNUSUAL_NETBLO
CK

Authentication from
an unusual network
block

Observation

Unusual network
block used

auth

UNUSUAL_TIME_OF
_DAY

Authentication
attempt during an
unusual time of day

Observation

Anomalous login
time

auth

USER_MARKED_FRA User explicitly
UD
marked
authentication
attempt as
fraudulent

Threat

Confirmed fraud by
user

bypass_status_enabl ed

Bypass status was
Control Violation
enabled for a user or
group

Enabling bypass
status weakens
authentication
posture

device_registration

REGISTER_INACTIV Device registered for Audit
E_USER
an inactive user

Registration activity
for inactive user

device_registration

REGISTER_OS_OUT
DATED

Device with outdated Audit
OS registered

Device with outdated
OS may increase risk

device_registration

REGISTER_UNLOCK

Device was unlocked Audit
during registration

Device registration
completed after
unlock

device_registration

REGISTER_TAMPER
ED

Tampered device
registered

Device tampering
detected

Threat

