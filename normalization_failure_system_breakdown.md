# Normalization Failure Tracking & Resolution System - Work Breakdown

## Overview
This document provides a comprehensive breakdown of development stories for implementing a normalization failure tracking and resolution system for data connectors. The system will track, report, and resolve normalization failures with automated metrics, alerting, and remediation capabilities.

## Detailed Architecture Analysis

### Current Codebase Patterns Observed:

**Django Models:**
- `ActivityLog` model: Simple tracking with `connector` FK, `timestamp`, `message`, `log_level` (TextChoices)
- `Connector` model: UUID primary key, J<PERSON><PERSON><PERSON> for settings/bookmarks, proper indexing
- All models use `auto_now_add=True` for creation timestamps, `auto_now=True` for updates
- Foreign keys use `on_delete=models.RESTRICT` consistently
- Indexes follow pattern: `models.Index(fields=["field1", "field2"])`

**Admin Interface Patterns:**
- `ReadOnlyJSONData.render()` uses Pygments with HtmlFormatter for JSON display
- List displays: tuples with field names and custom methods
- Filters: `DropdownFilter` from `django_admin_listfilter_dropdown`
- Search fields: tuples of field paths including FK relationships
- Readonly fields: tuples for auto-generated fields

**Event Processing Pipeline:**
- `@normalize(normalize_event)` decorator wraps event sync methods
- `NormalizationGenerator` catches `(TypeError, KeyError, AttributeError, ValueError)`
- Errors logged via `logger.warning("Failed to normalize raw data", exc_info=True, extra={"item": raw_obj})`
- `IntegrationLogger.log_normalization_error()` handles detailed error formatting
- Events stored via `artifact_service.write_results()` using S3 paths

**S3 Integration:**
- Artifact keys: `organization_id={uuid}/technology_id={id}/v1/connector_id={uuid}/year=2024/month=01/day=15/{correlation_id}.jsonl`
- S3 URLs: `https://console.aws.amazon.com/s3/buckets/{bucket}?prefix={encoded_key}/`
- File system abstraction via `apps/fs/s3.py` with `use_fs()` factory

**New Relic Integration:**
- Custom attributes via `newrelic.agent.add_custom_attributes(context)`
- Context format: `[("cs_org_alias", "value"), ("cs_technology_id", "value")]`
- Event posting via HTTP API to `insights-collector.newrelic.com`

**Celery Tasks:**
- `@shared_task` decorator with proper error handling
- Beat schedule in `CELERY_BEAT_SCHEDULE` using `crontab()` expressions
- Cleanup tasks follow pattern: filter by date, bulk delete, optional vacuum

**Git SHA Tracking:**
- Available via environment: `VERSION_TAG = os.environ.get("VERSION_TAG", "local")`
- Build script uses: `git rev-parse HEAD` and `git rev-parse --short HEAD`

## Development Stories Breakdown

### Phase 1: Core Infrastructure (Foundation)

#### Story 1.1: Failed Normalization Model Implementation
**Estimated Time: 1.5 days**

**Description:** Create the core Django model to track normalization failures following existing patterns.

**Technical Implementation:**
- File: `apps/connectors/models/failed_normalization.py`
- Pattern: Follow `ActivityLog` model structure exactly
- Fields based on existing model patterns:
  ```python
  class FailedNormalization(models.Model):
      class Status(models.TextChoices):
          ERROR = "error", "Error"
          RESOLVED = "resolved", "Resolved"

      id = models.BigAutoField(primary_key=True)  # Like ActivityLog
      connector = models.ForeignKey("connectors.Connector", on_delete=models.RESTRICT, related_name="failed_normalizations")
      integration_id = models.UUIDField()  # From connector.id
      account_id = models.UUIDField()  # From connector.organization.account_id
      error_message = models.TextField()
      error_exception_class = models.CharField(max_length=255)
      s3_event_locator = models.TextField()  # Full S3 artifact key
      status = models.CharField(max_length=50, choices=Status.choices, default=Status.ERROR)
      date_created = models.DateTimeField(auto_now_add=True)
      date_updated = models.DateTimeField(auto_now=True)
      date_resolved = models.DateTimeField(null=True, blank=True)
      git_sha_created = models.CharField(max_length=40, default="unknown")
      git_sha_resolved = models.CharField(max_length=40, null=True, blank=True)
  ```

**Tasks:**
- Create model following exact `ActivityLog` patterns
- Add indexes: `["connector_id", "status"], ["date_created"], ["status", "date_created"]`
- Create migration using existing migration patterns
- Add to `apps/connectors/models/__init__.py`
- Create factory in `factories/failed_normalization.py`

**Dependencies:** None

**Acceptance Criteria:**
- Model matches existing codebase patterns exactly
- Migration follows existing naming conventions
- Proper foreign key relationships established
- Factory created for testing

---

#### Story 1.2: Normalization Failure Capture Integration
**Estimated Time: 2.5 days**

**Description:** Integrate failure capture into existing `NormalizationGenerator` error handling.

**Technical Implementation:**
- Modify `apps/connectors/integrations/actions/utils.py` `NormalizationGenerator.__next__()`
- Add failure recording to existing exception handling block
- Use existing `artifact_service.build_key()` for S3 locator generation
- Leverage existing `VERSION_TAG` environment variable for git SHA

**Tasks:**
- Extend `NormalizationGenerator` to capture failures to database
- Create `FailedNormalizationService` in `apps/connectors/services/failed_normalization_service.py`
- Add S3 event locator generation using existing `artifact_service.build_key()` pattern
- Integrate with existing error logging in `IntegrationLogger.log_normalization_error()`
- Add git SHA capture from `settings.VERSION_TAG`

**Dependencies:** Story 1.1

**Acceptance Criteria:**
- All normalization failures automatically captured
- S3 locators properly generated using existing patterns
- No performance impact on existing event processing
- Git SHA tracking implemented

---

### Phase 2: Metrics & Monitoring

#### Story 2.1: New Relic Metrics Integration
**Estimated Time: 2 days**

**Description:** Implement New Relic metrics reporting using existing patterns.

**Technical Implementation:**
- Follow existing `apps/tracing/context.py` patterns
- Use existing New Relic event posting from `scripts/write_build_info_to_newrelic.sh`
- Metrics format: `{"eventType": "NormalizationMetrics", "integration_id": "...", "failed_count": 5}`

**Tasks:**
- Create `NormalizationMetricsReporter` in `apps/connectors/metrics/normalization_metrics.py`
- Implement New Relic event posting using existing HTTP API patterns
- Add metrics collection in `InvokeTracer.on_normalization_error()` method
- Create metrics aggregation for fetch operation statistics
- Add S3 statistics writing using existing `artifact_service` patterns

**Dependencies:** Story 1.2

**Acceptance Criteria:**
- New Relic metrics posted using existing API patterns
- Statistics written to S3 using existing filesystem abstraction
- Metrics include: ingested, normalized, failed, skipped counts per integration_id
- Integration with existing `InvokeTracer` class

---

#### Story 2.2: Data Retention Policy Implementation
**Estimated Time: 1 day**

**Description:** Implement 60-day retention following existing `task_delete_old_activity_log_records.py` pattern.

**Technical Implementation:**
- File: `apps/connectors/tasks/task_delete_old_failed_normalizations.py`
- Pattern: Exact copy of `task_delete_old_activity_log_records.py` structure
- Schedule: Add to existing `CELERY_BEAT_SCHEDULE` in `core/settings.py`

**Tasks:**
- Create cleanup task following existing pattern exactly
- Add to `CELERY_BEAT_SCHEDULE` with `crontab(minute="0", hour="7")` (offset from activity log cleanup)
- Create management command `cleanup_failed_normalizations.py`
- Add unit tests following existing test patterns

**Dependencies:** Story 1.1

**Acceptance Criteria:**
- Task follows existing cleanup patterns exactly
- Scheduled for daily execution
- Management command available for manual cleanup
- Unit tests match existing test patterns

---

### Phase 3: Aggregation & Alerting

#### Story 3.1: Statistics Aggregation Task
**Estimated Time: 2 days**

**Description:** Implement 5-minute aggregation task following existing Celery patterns.

**Technical Implementation:**
- File: `apps/connectors/tasks/task_aggregate_normalization_stats.py`
- Pattern: Follow existing `task_sync_events.py` structure
- Schedule: `crontab(minute="*/5")` in `CELERY_BEAT_SCHEDULE`
- Query: `FailedNormalization.objects.filter(status=Status.ERROR).values('integration_id').annotate(count=Count('id'))`

**Tasks:**
- Create aggregation task following existing Celery task patterns
- Implement database aggregation using Django ORM
- Add New Relic metrics posting using existing HTTP API
- Add task to `CELERY_BEAT_SCHEDULE` with 5-minute interval
- Create unit tests following existing task test patterns

**Dependencies:** Stories 1.1, 2.1

**Acceptance Criteria:**
- Task follows existing Celery patterns exactly
- Aggregation uses efficient Django ORM queries
- New Relic metrics posted every 5 minutes
- Unit tests cover aggregation logic

---

#### Story 3.2: NRQL Alarms Configuration
**Estimated Time: 1.5 days**

**Description:** Create NRQL alarm configuration and documentation.

**Technical Implementation:**
- NRQL Query: `SELECT latest(failed_count) / latest(total_count) * 100 as failure_rate FROM NormalizationMetrics WHERE integration_id = '{id}' SINCE 10 minutes ago`
- Threshold: `failure_rate > 1.0`
- Alert per integration_id using New Relic API

**Tasks:**
- Create NRQL query templates for failure rate calculation
- Document alarm setup process in `docs/normalization_alarms.md`
- Create alarm configuration script using New Relic API
- Add alarm testing and validation procedures
- Create runbook for alarm response

**Dependencies:** Story 3.1

**Acceptance Criteria:**
- NRQL queries properly calculate 1% failure threshold
- Documentation includes step-by-step alarm setup
- Alarm configuration script functional
- Testing procedures documented

---

### Phase 4: User Interface & Management

#### Story 4.1: Django Admin Interface Implementation
**Estimated Time: 1.5 days**

**Description:** Create Django admin interface following existing patterns exactly.

**Technical Implementation:**
- File: `apps/connectors/admin/failed_normalization_admin.py`
- Pattern: Follow `ActivityLogAdmin` structure exactly
- Default queryset: `queryset.exclude(status=Status.RESOLVED)`

**Tasks:**
- Create admin class following existing `ActivityLogAdmin` patterns:
  ```python
  @admin.register(FailedNormalization)
  class FailedNormalizationAdmin(admin.ModelAdmin):
      list_display = ("id", "connector_id", "integration_id", "error_exception_class", "status", "date_created")
      list_filter = [
          ("connector__id", DropdownFilter),
          ("integration_id", DropdownFilter),
          "status",
          "error_exception_class",
      ]
      search_fields = ("error_message", "s3_event_locator")
      readonly_fields = ("date_created", "date_updated", "git_sha_created")
  ```
- Add to `apps/connectors/admin/__init__.py`
- Implement default filtering to hide resolved items
- Add custom methods for display formatting

**Dependencies:** Story 1.1

**Acceptance Criteria:**
- Admin interface follows existing patterns exactly
- Default view excludes resolved items
- Filtering and search work as expected
- Registered in admin init file

---

#### Story 4.2: Pretty-Printed JSON Display & S3 Integration
**Estimated Time: 1 day**

**Description:** Add JSON display and S3 event viewing using existing `ReadOnlyJSONData` pattern.

**Technical Implementation:**
- Use existing `ReadOnlyJSONData.render()` method from `connector_admin.py`
- Add S3 URL generation using existing `artifact_service.get_url()` method
- Pattern: Follow `health_check_results()` method in `ConnectorAdmin`

**Tasks:**
- Add `event_data_display()` method using existing `ReadOnlyJSONData.render()`
- Add `s3_event_link()` method using existing `artifact_service.get_url()`
- Implement event data retrieval from S3 using existing `artifact_service.read_lines()`
- Add readonly fields for JSON display and S3 links
- Follow existing admin method patterns exactly

**Dependencies:** Story 4.1

**Acceptance Criteria:**
- JSON display uses existing `ReadOnlyJSONData` patterns
- S3 links generated using existing URL patterns
- Event data retrieved using existing artifact service
- Display matches existing admin interface styling

---

### Phase 5: Resolution Workflow

#### Story 5.1: S3 Export Functionality
**Estimated Time: 2 days**

**Description:** Add admin export action using existing Django admin patterns.

**Technical Implementation:**
- Pattern: Follow existing Django admin action patterns
- Export format: JSON Lines (.jsonl) matching existing artifact format
- S3 path: Use existing `artifact_service.build_key()` pattern with export prefix

**Tasks:**
- Add admin action `export_failed_events_to_s3()` following Django admin action patterns
- Implement S3 export using existing `artifact_service.write_results()` method
- Create export path using existing artifact key patterns
- Add export status messages using Django admin messaging
- Implement bulk selection handling

**Dependencies:** Stories 4.1, 4.2

**Acceptance Criteria:**
- Admin action follows existing Django patterns
- Export uses existing S3 infrastructure
- Export format matches existing artifact format
- Status messages displayed to users

---

#### Story 5.2: Automated Jira Ticket Creation
**Estimated Time: 2.5 days**

**Description:** Implement Jira ticket creation using existing integration patterns.

**Technical Implementation:**
- Use existing `JiraV1CreateIssue` action from `apps/connectors/integrations/vendors/atlassian/jira/v1/`
- Webhook handler: FastAPI endpoint following existing API patterns
- Ticket template: Use existing `get_issue_fields()` function

**Tasks:**
- Create webhook endpoint in `apps/api/v1/endpoints/` following existing patterns
- Implement ticket creation using existing `JiraV1CreateIssue` action
- Create ticket template for normalization failures
- Add webhook authentication using existing auth patterns
- Implement ticket tracking in `FailedNormalization` model

**Dependencies:** Story 3.2

**Acceptance Criteria:**
- Webhook endpoint follows existing API patterns
- Ticket creation uses existing Jira integration
- Ticket content includes failure details and S3 links
- Webhook authentication implemented

---

#### Story 5.3: Re-ingestion Management Command
**Estimated Time: 3 days**

**Description:** Create management command following existing command patterns.

**Technical Implementation:**
- File: `apps/connectors/management/commands/reingest_failed_events.py`
- Pattern: Follow existing `update_aad_app_secret.py` structure
- Event annotation: Add to raw_event dict before reprocessing
- Reprocessing: Use existing `invoke_integration_action` task

**Tasks:**
- Create management command following existing patterns
- Implement S3 event retrieval using existing `artifact_service.read_lines()`
- Add `criticalstart_reprocessed: True` annotation to event data
- Implement batch processing with Django ORM bulk operations
- Add progress tracking using existing logging patterns
- Update status to resolved after successful reprocessing

**Dependencies:** Stories 1.1, 1.2

**Acceptance Criteria:**
- Command follows existing management command patterns
- Event retrieval uses existing artifact service
- Event annotation properly implemented
- Status updates use existing model patterns

---

#### Story 5.4: Automatic Stat Re-aggregation
**Estimated Time: 1.5 days**

**Description:** Trigger aggregation after reprocessing using existing task patterns.

**Technical Implementation:**
- Use existing `task_aggregate_normalization_stats` task
- Trigger via `apply_async()` method following existing patterns
- Add to re-ingestion command completion

**Tasks:**
- Modify re-ingestion command to trigger aggregation task
- Use existing Celery `apply_async()` patterns
- Add immediate aggregation after batch completion
- Implement aggregation timing configuration
- Add monitoring using existing logging patterns

**Dependencies:** Stories 3.1, 5.3

**Acceptance Criteria:**
- Aggregation triggered using existing Celery patterns
- Immediate aggregation after reprocessing
- Configuration follows existing settings patterns
- Monitoring uses existing logging infrastructure

---

## Implementation Order & Dependencies

### Recommended Implementation Sequence:

1. **Phase 1 (Foundation)**: Stories 1.1 → 1.2
2. **Phase 2 (Metrics)**: Stories 2.1 → 2.2
3. **Phase 3 (Aggregation)**: Stories 3.1 → 3.2
4. **Phase 4 (UI)**: Stories 4.1 → 4.2
5. **Phase 5 (Resolution)**: Stories 5.1 → 5.2 → 5.3 → 5.4

### Critical Path Dependencies:
- Story 1.1 is prerequisite for all other stories (provides core model)
- Story 1.2 enables failure capture for all subsequent features
- Story 2.1 required for New Relic metrics in Story 3.1
- Story 3.1 must complete before 3.2 for alarm configuration
- Stories 4.1-4.2 can be developed in parallel with Phase 3
- Story 5.3 required before 5.4 for automatic re-aggregation

## Revised Total Estimated Time: 19.5 days

### Phase Breakdown:
- **Phase 1**: 4 days (1.5 + 2.5)
- **Phase 2**: 3 days (2 + 1)
- **Phase 3**: 3.5 days (2 + 1.5)
- **Phase 4**: 2.5 days (1.5 + 1)
- **Phase 5**: 9 days (2 + 2.5 + 3 + 1.5)

## Technical Risk Assessment:

### Low Risk (Well-Established Patterns):
- **Model Creation (1.1)**: Exact copy of `ActivityLog` patterns
- **Admin Interface (4.1-4.2)**: Existing `ReadOnlyJSONData` and admin patterns
- **Celery Tasks (2.2, 3.1)**: Existing task patterns well-established
- **Management Commands (5.3)**: Existing command patterns available

### Medium Risk (Requires Integration):
- **Failure Capture (1.2)**: Modifying existing `NormalizationGenerator` - requires careful testing
- **New Relic Integration (2.1)**: Using existing patterns but new metrics format
- **S3 Export (5.1)**: Using existing infrastructure but new export workflow

### Higher Risk (External Dependencies):
- **NRQL Alarms (3.2)**: Requires New Relic infrastructure coordination
- **Jira Integration (5.2)**: Depends on existing Jira connector configuration
- **Re-ingestion (5.3)**: Complex workflow with event reprocessing

## Performance Considerations:

### Database Impact:
- **FailedNormalization** table: Estimated 1-5% of total events (based on current error rates)
- **Indexes**: Properly indexed for query performance
- **Retention**: 60-day cleanup prevents unbounded growth

### Event Processing Impact:
- **Failure Capture**: Minimal overhead - only executes on existing exception path
- **S3 Locator Generation**: Uses existing `artifact_service.build_key()` - no additional overhead

### New Relic Impact:
- **Metrics Volume**: Aggregated metrics every 5 minutes per integration_id
- **API Calls**: Batched using existing HTTP API patterns

## Testing Strategy:

### Unit Tests (Required for each story):
- Model tests following existing `test_activity_log.py` patterns
- Admin tests following existing admin test patterns
- Task tests following existing `test_task_delete_old_activity_log_records.py` patterns
- Service tests for new service classes

### Integration Tests:
- End-to-end failure capture and database storage
- New Relic metrics posting and retrieval
- S3 export and re-ingestion workflows
- Jira ticket creation and tracking

### Performance Tests:
- Event processing throughput with failure capture enabled
- Database query performance with failure table populated
- Memory usage during bulk export operations

This revised breakdown provides a more accurate and realistic implementation plan based on detailed analysis of existing codebase patterns and infrastructure capabilities.
