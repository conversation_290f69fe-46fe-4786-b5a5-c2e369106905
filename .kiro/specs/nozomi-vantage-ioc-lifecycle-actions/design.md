# Design Document: Nozomi Vantage IOC Lifecycle Actions

## Overview

This design document outlines the implementation approach for adding IOC (Indicator of Compromise) lifecycle actions to the Nozomi Vantage Data Connector. The feature will enable security analysts to create, update, delete, and retrieve IOCs through the Nozomi Vantage integration, enhancing threat response capabilities.

## Architecture

The implementation will follow a layered architecture pattern:

1. **API Layer**: Handles HTTP requests/responses to/from the Nozomi Vantage API
2. **Service Layer**: Contains business logic for IOC lifecycle management
3. **Model Layer**: Defines data structures for IOCs and related entities
4. **Logging Layer**: Manages audit logging for all IOC actions

The feature will integrate with the existing data connector framework while adding new capabilities specific to IOC lifecycle management.

```mermaid
graph TD
    A[Client Application] --> B[API Layer]
    B --> C[Service Layer]
    C --> D[Model Layer]
    C --> E[Nozomi Vantage API]
    C --> F[Logging Layer]
    F --> G[Audit Log Storage]
```

## Components and Interfaces

### 1. IOC Model

The IOC model will represent the structure of Indicators of Compromise in Nozomi Vantage:

```python
class IOC:
    id: str  # Unique identifier
    type: str  # Type of IOC (IP, URL, hash, etc.)
    value: str  # The actual indicator value
    source: str  # Source of the IOC
    severity: str  # Severity level (high, medium, low)
    confidence: float  # Confidence score (0-100)
    description: str  # Description of the threat
    tags: List[str]  # Associated tags
    created_at: datetime  # Creation timestamp
    updated_at: datetime  # Last update timestamp
    created_by: str  # User who created the IOC
    status: str  # Status of the IOC (active, expired, etc.)
```

### 2. Nozomi Vantage API Client

The API client will handle communication with the Nozomi Vantage API:

```python
class NozomiVantageClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.api_key = api_key
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        # Set up session with authentication headers
        pass
    
    def create_ioc(self, ioc_data: dict) -> dict:
        # Send POST request to create IOC
        pass
    
    def update_ioc(self, ioc_id: str, ioc_data: dict) -> dict:
        # Send PUT request to update IOC
        pass
    
    def delete_ioc(self, ioc_id: str) -> bool:
        # Send DELETE request to remove IOC
        pass
    
    def get_ioc(self, ioc_id: str) -> dict:
        # Send GET request to retrieve specific IOC
        pass
    
    def list_iocs(self, filters: dict = None, page: int = 1, page_size: int = 100) -> dict:
        # Send GET request to list IOCs with pagination and filtering
        pass
```

### 3. IOC Service

The service layer will implement business logic for IOC management:

```python
class IOCService:
    def __init__(self, client: NozomiVantageClient, logger: AuditLogger):
        self.client = client
        self.logger = logger
    
    def create_ioc(self, ioc_data: dict, user: str) -> IOC:
        # Validate IOC data
        # Create IOC via client
        # Log action
        # Return created IOC
        pass
    
    def update_ioc(self, ioc_id: str, ioc_data: dict, user: str) -> IOC:
        # Validate IOC data
        # Update IOC via client
        # Log action
        # Return updated IOC
        pass
    
    def delete_ioc(self, ioc_id: str, user: str) -> bool:
        # Delete IOC via client
        # Log action
        # Return success status
        pass
    
    def get_ioc(self, ioc_id: str) -> IOC:
        # Get IOC via client
        # Return IOC object
        pass
    
    def list_iocs(self, filters: dict = None, page: int = 1, page_size: int = 100) -> Tuple[List[IOC], int]:
        # List IOCs via client with pagination and filtering
        # Return list of IOCs and total count
        pass
```

### 4. Audit Logger

The audit logger will record all IOC lifecycle actions:

```python
class AuditLogger:
    def __init__(self, log_storage):
        self.log_storage = log_storage
    
    def log_ioc_action(self, action: str, user: str, ioc_id: str, details: dict = None) -> bool:
        # Create log entry
        # Store log entry
        # Return success status
        pass
```

### 5. API Endpoints

New API endpoints will be added to expose the IOC lifecycle actions:

```
POST /api/v1/integrations/nozomi-vantage/iocs
PUT /api/v1/integrations/nozomi-vantage/iocs/{ioc_id}
DELETE /api/v1/integrations/nozomi-vantage/iocs/{ioc_id}
GET /api/v1/integrations/nozomi-vantage/iocs/{ioc_id}
GET /api/v1/integrations/nozomi-vantage/iocs
```

## Data Models

### IOC Request Model

```python
class IOCRequest:
    type: str  # Required
    value: str  # Required
    source: str  # Required
    severity: str  # Required
    confidence: float  # Optional
    description: str  # Optional
    tags: List[str]  # Optional
    status: str  # Optional, defaults to "active"
```

### IOC Response Model

```python
class IOCResponse:
    id: str
    type: str
    value: str
    source: str
    severity: str
    confidence: float
    description: str
    tags: List[str]
    created_at: datetime
    updated_at: datetime
    created_by: str
    status: str
```

### Audit Log Model

```python
class AuditLogEntry:
    id: str
    timestamp: datetime
    user: str
    action: str  # "create", "update", "delete", "get", "list"
    resource_type: str  # "ioc"
    resource_id: str
    details: dict
```

## Error Handling

The implementation will include comprehensive error handling:

1. **Validation Errors**: Return 400 Bad Request with details about validation failures
2. **Authentication Errors**: Return 401 Unauthorized for API key issues
3. **Authorization Errors**: Return 403 Forbidden for permission issues
4. **Not Found Errors**: Return 404 Not Found when IOCs don't exist
5. **API Errors**: Return appropriate error codes based on Nozomi Vantage API responses
6. **Internal Errors**: Return 500 Internal Server Error for unexpected issues

All errors will be logged with appropriate context for troubleshooting.

## Testing Strategy

The implementation will include the following types of tests:

1. **Unit Tests**:
   - Test IOC validation logic
   - Test service layer business logic
   - Test error handling

2. **Integration Tests**:
   - Test API client with mock Nozomi Vantage API
   - Test end-to-end API flows

3. **Mock Tests**:
   - Create mock responses for Nozomi Vantage API
   - Test error scenarios

4. **Security Tests**:
   - Verify authentication and authorization
   - Test input validation and sanitization

5. **Performance Tests**:
   - Test pagination with large datasets
   - Test response times for common operations