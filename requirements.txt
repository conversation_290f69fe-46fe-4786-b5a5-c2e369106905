#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile --no-emit-index-url --strip-extras requirements.in
#
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.11.14
    # via langchain-community
aiosignal==1.3.2
    # via aiohttp
amqp==5.3.1
    # via kombu
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   gql
    #   httpx
    #   openai
    #   starlette
arrow==1.3.0
    # via restfly
asgiref==3.8.1
    # via
    #   django
    #   django-cors-headers
ata-common==1.1.2
    # via
    #   -r requirements.in
    #   microsoft-client
attrs==25.3.0
    # via
    #   aiohttp
    #   jsonschema
    #   referencing
authlib==1.5.1
    # via
    #   criticalstart-django-admin-sso
    #   microsoft-client
backoff==2.2.1
    # via gql
beautifulsoup4==4.13.4
    # via
    #   -r requirements.in
    #   ata-common
billiard==4.2.1
    # via celery
boto3==1.37.22
    # via
    #   ata-common
    #   carbon-black-cloud-sdk
botocore==1.37.22
    # via
    #   boto3
    #   s3transfer
cachetools==5.5.2
    # via cbapi
carbon-black-cloud-sdk==1.5.7
    # via -r requirements.in
cbapi==2.0.0
    # via -r requirements.in
celery==5.5.2
    # via
    #   -r requirements.in
    #   celery-amqp-backend
    #   django-celery-beat
celery-amqp-backend==1.2.0
    # via -r requirements.in
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
    #   requests
    #   sentry-sdk
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
    #   uvicorn
click-didyoumean==0.3.1
    # via celery
click-plugins==1.1.1
    # via celery
click-repl==0.3.0
    # via celery
colorlog==6.9.0
    # via -r requirements.in
configcat-client==9.0.4
    # via ata-common
criticalstart-artifacts-sdk==1.0.3
    # via -r requirements.in
criticalstart-auth==0.0.31
    # via -r requirements.in
criticalstart-django-admin-sso==0.0.11
    # via -r requirements.in
criticalstart-django-admin-themes==0.0.4
    # via -r requirements.in
criticalstart-fastapi-utils==0.0.27
    # via -r requirements.in
criticalstart-service-bus==1.3.0
    # via -r requirements.in
cron-descriptor==1.4.5
    # via django-celery-beat
crowdstrike-falconpy==1.4.9
    # via -r requirements.in
cryptography==44.0.2
    # via
    #   ata-common
    #   authlib
dataclasses-json==0.6.7
    # via
    #   -r requirements.in
    #   langchain-community
deepdiff==8.4.2
    # via -r requirements.in
defusedxml==0.7.1
    # via pytenable
distro==1.9.0
    # via openai
dj-database-url==2.3.0
    # via -r requirements.in
django==4.2.23
    # via
    #   -r requirements.in
    #   ata-common
    #   criticalstart-django-admin-themes
    #   dj-database-url
    #   django-celery-beat
    #   django-cors-headers
    #   django-custom-user
    #   django-extensions
    #   django-phonenumber-field
    #   django-timezone-field
django-admin-list-filter-dropdown==1.0.3
    # via -r requirements.in
django-celery-beat==2.8.1
    # via -r requirements.in
django-cors-headers==4.7.0
    # via -r requirements.in
django-custom-user==1.1
    # via -r requirements.in
django-extensions==4.1
    # via -r requirements.in
django-phonenumber-field==8.0.0
    # via ata-common
django-timezone-field==7.1
    # via
    #   ata-common
    #   django-celery-beat
dnspython==2.7.0
    # via email-validator
email-validator==2.2.0
    # via -r requirements.in
enum-compat==0.0.3
    # via configcat-client
et-xmlfile==2.0.0
    # via openpyxl
faker==37.4.2
    # via -r requirements.in
fastapi==0.115.12
    # via
    #   -r requirements.in
    #   criticalstart-auth
    #   criticalstart-fastapi-utils
frozenlist==1.5.0
    # via
    #   aiohttp
    #   aiosignal
gql==3.5.2
    # via
    #   -r requirements.in
    #   pytenable
graphql-core==3.2.4
    # via
    #   gql
    #   pytenable
greenlet==3.2.2
    # via sqlalchemy
gunicorn==23.0.0
    # via -r requirements.in
h11==0.14.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.7
    # via httpx
httpx==0.28.1
    # via
    #   criticalstart-auth
    #   langsmith
    #   openai
httpx-sse==0.4.0
    # via langchain-community
idna==3.10
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   requests
    #   yarl
iniconfig==2.1.0
    # via pytest
jiter==0.9.0
    # via openai
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
jsonschema==4.23.0
    # via carbon-black-cloud-sdk
jsonschema-specifications==2024.10.1
    # via jsonschema
kombu==5.5.3
    # via
    #   ata-common
    #   celery
    #   criticalstart-service-bus
langchain==0.3.21
    # via langchain-community
langchain-community==0.3.20
    # via -r requirements.in
langchain-core==0.3.49
    # via
    #   langchain
    #   langchain-community
    #   langchain-openai
    #   langchain-text-splitters
langchain-openai==0.3.12
    # via -r requirements.in
langchain-text-splitters==0.3.7
    # via langchain
langsmith==0.3.19
    # via
    #   langchain
    #   langchain-community
    #   langchain-core
lxml==5.3.1
    # via qualysapi
marshmallow==3.26.1
    # via
    #   dataclasses-json
    #   pytenable
microsoft-client==0.1.2
    # via -r requirements.in
multidict==6.2.0
    # via
    #   aiohttp
    #   yarl
mypy-extensions==1.0.0
    # via typing-inspect
newrelic==10.13.0
    # via -r requirements.in
numpy==2.2.6
    # via
    #   -r requirements.in
    #   langchain-community
openai==1.69.0
    # via langchain-openai
openpyxl==3.1.5
    # via -r requirements.in
orderly-set==5.3.0
    # via deepdiff
orjson==3.10.16
    # via langsmith
packaging==24.2
    # via
    #   cbapi
    #   gunicorn
    #   langchain-core
    #   langsmith
    #   marshmallow
    #   pytest
phonenumbers==9.0.2
    # via ata-common
pika==1.3.2
    # via cbapi
pluggy==1.5.0
    # via pytest
prompt-toolkit==3.0.50
    # via
    #   cbapi
    #   click-repl
propcache==0.3.1
    # via
    #   aiohttp
    #   yarl
protobuf==6.31.1
    # via cbapi
psycopg2-binary==2.9.10
    # via -r requirements.in
pycparser==2.22
    # via cffi
pydantic==2.11.1
    # via
    #   -r requirements.in
    #   criticalstart-artifacts-sdk
    #   criticalstart-auth
    #   criticalstart-service-bus
    #   fastapi
    #   langchain
    #   langchain-core
    #   langsmith
    #   openai
    #   pydantic-extra-types
    #   pydantic-settings
    #   pytenable
pydantic-core==2.33.0
    # via pydantic
pydantic-extra-types==2.10.3
    # via
    #   -r requirements.in
    #   pytenable
pydantic-settings==2.8.1
    # via langchain-community
pydash==8.0.5
    # via criticalstart-fastapi-utils
pygments==2.19.1
    # via cbapi
pyjwt==2.10.1
    # via
    #   ata-common
    #   criticalstart-auth
    #   criticalstart-django-admin-sso
pyotp==2.9.0
    # via ata-common
pytenable==1.7.4
    # via -r requirements.in
pytest==8.3.5
    # via
    #   pytest-django
    #   pytest-mock
pytest-django==4.11.1
    # via -r requirements.in
pytest-mock==3.14.0
    # via -r requirements.in
python-box==7.3.2
    # via
    #   pytenable
    #   restfly
python-crontab==3.2.0
    # via django-celery-beat
python-dateutil==2.9.0.post0
    # via
    #   arrow
    #   ata-common
    #   botocore
    #   carbon-black-cloud-sdk
    #   cbapi
    #   celery
    #   criticalstart-artifacts-sdk
    #   pytenable
    #   python-crontab
python-dotenv==1.1.0
    # via pydantic-settings
pyyaml==6.0.2
    # via
    #   carbon-black-cloud-sdk
    #   cbapi
    #   langchain
    #   langchain-community
    #   langchain-core
qualname==0.1.0
    # via configcat-client
qualysapi==8.1.0+criticalstart1
    # via -r requirements.in
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2024.11.6
    # via tiktoken
requests==2.32.4
    # via
    #   ata-common
    #   carbon-black-cloud-sdk
    #   cbapi
    #   configcat-client
    #   criticalstart-django-admin-sso
    #   crowdstrike-falconpy
    #   gql
    #   langchain
    #   langchain-community
    #   langsmith
    #   microsoft-client
    #   pytenable
    #   qualysapi
    #   requests-toolbelt
    #   restfly
    #   tiktoken
    #   unificontrol
requests-toolbelt==1.0.0
    # via
    #   gql
    #   langsmith
    #   pytenable
restfly==1.5.1
    # via pytenable
rpds-py==0.24.0
    # via
    #   jsonschema
    #   referencing
s3transfer==0.11.4
    # via boto3
schema==0.7.7
    # via carbon-black-cloud-sdk
semver==3.0.4
    # via
    #   configcat-client
    #   pytenable
sentry-sdk==2.29.1
    # via -r requirements.in
six==1.17.0
    # via
    #   python-dateutil
    #   treelib
sniffio==1.3.1
    # via
    #   anyio
    #   openai
solrq==1.1.2
    # via
    #   carbon-black-cloud-sdk
    #   cbapi
soupsieve==2.6
    # via beautifulsoup4
sqlalchemy==2.0.40
    # via
    #   langchain
    #   langchain-community
sqlparse==0.5.3
    # via django
starlette==0.46.1
    # via fastapi
tenacity==9.0.0
    # via
    #   langchain-community
    #   langchain-core
tiktoken==0.9.0
    # via langchain-openai
tqdm==4.67.1
    # via openai
treelib==1.7.1
    # via ata-common
types-python-dateutil==2.9.0.20241206
    # via arrow
typing-extensions==4.13.0
    # via
    #   anyio
    #   beautifulsoup4
    #   criticalstart-artifacts-sdk
    #   dj-database-url
    #   fastapi
    #   langchain-core
    #   openai
    #   pydantic
    #   pydantic-core
    #   pydantic-extra-types
    #   pydash
    #   pytenable
    #   referencing
    #   restfly
    #   sqlalchemy
    #   typing-inspect
    #   typing-inspection
typing-inspect==0.9.0
    # via dataclasses-json
typing-inspection==0.4.0
    # via pydantic
tzdata==2025.2
    # via
    #   django-celery-beat
    #   faker
    #   kombu
unificontrol==0.2.9
    # via -r requirements.in
urllib3==2.0.7
    # via
    #   botocore
    #   criticalstart-artifacts-sdk
    #   crowdstrike-falconpy
    #   pytenable
    #   requests
    #   sentry-sdk
uvicorn==0.34.3
    # via -r requirements.in
validators==0.34.0
    # via
    #   carbon-black-cloud-sdk
    #   cbapi
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
wcwidth==0.2.13
    # via prompt-toolkit
whitenoise==6.9.0
    # via -r requirements.in
xmltodict==0.14.2
    # via -r requirements.in
yarl==1.18.3
    # via
    #   aiohttp
    #   gql
zstandard==0.23.0
    # via langsmith
