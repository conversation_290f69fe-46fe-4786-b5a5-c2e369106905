#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile --no-emit-index-url --strip-extras requirements_dev.in
#
annotated-types==0.7.0
    # via
    #   -c requirements.txt
    #   pydantic
anyio==4.9.0
    # via
    #   -c requirements.txt
    #   httpx
argcomplete==3.6.2
    # via datamodel-code-generator
black==23.7.0
    # via
    #   -r requirements_dev.in
    #   datamodel-code-generator
build==1.2.2.post1
    # via pip-tools
certifi==2025.1.31
    # via
    #   -c requirements.txt
    #   httpcore
    #   httpx
    #   requests
cfgv==3.4.0
    # via pre-commit
charset-normalizer==3.4.1
    # via
    #   -c requirements.txt
    #   requests
click==8.1.8
    # via
    #   -c requirements.txt
    #   black
    #   pip-tools
coverage==7.8.0
    # via pytest-cov
criticalstart-auth==0.0.31
    # via
    #   -c requirements.txt
    #   -r requirements_dev.in
datamodel-code-generator==0.30.1
    # via -r requirements_dev.in
distlib==0.3.9
    # via virtualenv
factory-boy==3.3.3
    # via
    #   -r requirements_dev.in
    #   criticalstart-auth
faker==37.4.2
    # via
    #   -c requirements.txt
    #   factory-boy
filelock==3.18.0
    # via virtualenv
genson==1.3.0
    # via datamodel-code-generator
h11==0.14.0
    # via
    #   -c requirements.txt
    #   httpcore
httpcore==1.0.7
    # via
    #   -c requirements.txt
    #   httpx
httpx==0.28.1
    # via
    #   -c requirements.txt
    #   respx
identify==2.6.10
    # via pre-commit
idna==3.10
    # via
    #   -c requirements.txt
    #   anyio
    #   httpx
    #   requests
inflect==7.5.0
    # via datamodel-code-generator
iniconfig==2.1.0
    # via
    #   -c requirements.txt
    #   pytest
isort==6.0.1
    # via datamodel-code-generator
jinja2==3.1.6
    # via datamodel-code-generator
markupsafe==3.0.2
    # via jinja2
more-itertools==10.7.0
    # via inflect
mypy-extensions==1.0.0
    # via
    #   -c requirements.txt
    #   black
nodeenv==1.9.1
    # via pre-commit
packaging==24.2
    # via
    #   -c requirements.txt
    #   black
    #   build
    #   datamodel-code-generator
    #   pytest
parameterized==0.9.0
    # via -r requirements_dev.in
pathspec==0.12.1
    # via black
pip-tools==7.4.1
    # via -r requirements_dev.in
platformdirs==4.3.8
    # via
    #   black
    #   virtualenv
pluggy==1.5.0
    # via
    #   -c requirements.txt
    #   pytest
pre-commit==4.2.0
    # via -r requirements_dev.in
pydantic==2.11.1
    # via
    #   -c requirements.txt
    #   criticalstart-auth
    #   datamodel-code-generator
pydantic-core==2.33.0
    # via
    #   -c requirements.txt
    #   pydantic
pyjwt==2.10.1
    # via
    #   -c requirements.txt
    #   criticalstart-auth
pyproject-hooks==1.2.0
    # via
    #   build
    #   pip-tools
pytest==8.3.5
    # via
    #   -c requirements.txt
    #   -r requirements_dev.in
    #   criticalstart-auth
    #   pytest-cov
    #   pytest-django
    #   pytest-env
    #   pytest-mock
pytest-cov==6.1.1
    # via -r requirements_dev.in
pytest-django==4.11.1
    # via
    #   -c requirements.txt
    #   -r requirements_dev.in
pytest-env==1.1.5
    # via -r requirements_dev.in
pytest-mock==3.14.0
    # via
    #   -c requirements.txt
    #   -r requirements_dev.in
pytz==2025.2
    # via -r requirements_dev.in
pyyaml==6.0.2
    # via
    #   -c requirements.txt
    #   datamodel-code-generator
    #   pre-commit
    #   responses
requests==2.32.4
    # via
    #   -c requirements.txt
    #   responses
responses==0.25.7
    # via -r requirements_dev.in
respx==0.22.0
    # via -r requirements_dev.in
sniffio==1.3.1
    # via
    #   -c requirements.txt
    #   anyio
tomli==2.2.1
    # via datamodel-code-generator
typeguard==4.4.2
    # via inflect
typing-extensions==4.13.0
    # via
    #   -c requirements.txt
    #   anyio
    #   pydantic
    #   pydantic-core
    #   typeguard
    #   typing-inspection
typing-inspection==0.4.0
    # via
    #   -c requirements.txt
    #   pydantic
tzdata==2025.2
    # via
    #   -c requirements.txt
    #   faker
urllib3==2.0.7
    # via
    #   -c requirements.txt
    #   requests
    #   responses
virtualenv==20.31.2
    # via pre-commit
wheel==0.45.1
    # via pip-tools
