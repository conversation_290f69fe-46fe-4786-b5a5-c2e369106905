DI - Proofpoint
Actions
Portal Action
(TAP)

Type

Data Connector Operates on
Action
Identifier Class
(Entity)

API Call

Notes

decode_url

Investigation

decode_url

URL

URL Decoder
API Proofpoint, Inc.

Returns encoded
and decode URL
along with mail
message
containing the
URL. We need to
map only the
decoded URL
using OCSF URL
object mapped
below

get_email_th
reat

EmailThreatI Threats API dentifier
Proofpoint, Inc.

Deferred until a
future release
Requires
extension to
OSINT. There is
an OPEN PR in
OCSF GH Repo
that will be
needed: OSINT

get_email_th Investigation
reat

extension by
<PERSON><PERSON><PERSON><PERSON> ·
Pull Request
#1310 ·
ocsf/ocsfschema
get_threat_f Investigations
orensics

get_threat_f
orensics

EmailThreatI Forensics API Proofpoint, Inc.
dentifier

Deferred until a
future release
Returns forensic
evidence about
a specific threat.
An example
would be an
attachment

© 2025 Critical Start All rights reserved

Page 1

containing a
known bad MD5
hash.

Field

OCSF Mapping (URL)

encodedUrl

decodedUrl

Ignored Reason

Example Value

Normalized Field NA

A string, the original,
rewritten URL supplied to
the endpoint.

url_string

A string, the target URL
embedded inside the
rewritten link.

messageGuid

Normalized Field NA

A string, the PPS GUID of
the message which
originally contained the
URL.

recipientEmail

Normalized Field NA

A string, the email address
of the messages' original
recipient.

clusterName

Normalized Field NA

A string, the name of the
PPS cluster which rewrote
the message.

success

Normalized Field NA

A boolean, indicates
whether the URL could
successfully be decoded

error

Normalized Field NA

A string, indicates what
error occurred when
attempting to decode input

Alerts
API Type

API

Parameter To Use

Notes

GET All SIEM events

SIEM API - Proofpoint,
Inc.

sinceTime

Returns an object
containing four main
attributes:

messagesDelivere
d
messagesBlocked
clicksPermitted
© 2025 Critical Start All rights reserved

Page 2

clicksBlocked
Messages and Clicks will
be fetched in the same
call, but normalized using
different OCSF schemas
described below.
OCSF Schema Mapping for Messages

Field

OCSF Mapping (Email
Activity)

Ignored Reason

Example Value

ocsf.activity = Receive

hardcoded

ocsf.category = Network
Activity

hardcoded

ocsf.class = Email Activity

hardcoded

ocsf.type = Email Activity:
Receive

hardcoded

If Blocked:

Derived from the field
name

ocsf.action = Action.DENIED
ocsf.disposition =
Disposition.BLOCKED.
If Delivered:
ocsf.action =
Action.ALLOWED
ocsf.disposition =
Disposition.ALLOWED
ccAddresses

ocsf.email.cc

A list of email addresses
contained within the CC:
header, excluding friendly
names.

clusterId

Normalized Field NA

The name of the PPS
cluster which processed
the message.

completelyRewritten

Normalized Field NA

Indicates if URLS
contained in the message

© 2025 Critical Start All rights reserved

Page 3

were rewritten
fromAddress

ocsf.email.from

The email address
contained in the From:
header, excluding friendly
name.

GUID

ocsf.email.uid

The ID of the message
within PPS. It can be used
to identify the message in
PPS and is guaranteed to
be unique.
Note: Using the GUID for
finding_info may be wrong,
but we lack understanding
of the proofpoint data.

headerFrom

ocsf.email.from_mailbox

The full content of the
From: header, including
any friendly name.

headerReplyTo

ocsf.email.reply_to_mailboxes

If present, the full content
of the Reply-To: header,
including any friendly
names.

impostorScore

Normalized Field NA

Value from 0 - 100.
Impostor score of the
message. Higher scores
indicate higher certainty.

malwareScore

Normalized Field NA

Value from 0 - 100.
Malware score of the
message. Higher scores
indicate higher certainty.

messageID

ocsf.email.message_uid

Message-ID extracted
from the headers of the
email message. It can be
used to look up the
associated message in
PPS and is not unique.

messageParts.filenam
e

ocsf.email.file.name

An array of structures
containing content type,
disposition, filename,
sha256, md5, etc.

© 2025 Critical Start All rights reserved

Page 4

messageParts.md5

ocsf.email.file.hashes

The MD5 hash of the
messagePart contents.

.algorithm =
HashAlgorithm.MD5
.value = messageParts.md5
messageParts.oConten ocsf.email.file.mime_type
tType

The declared ContentType of the messagePart.

messageParts.sandbo
xStatus

ocsf.email.file.security_descri
ptor

The verdict returned by
sandbox (e.g.
unsupported, threat,
prefilter, clean, uploaded,
inprogress,
uploaddisabled)

messageParts.sha256

ocsf.email.file.hashes

The SHA256 hash of the
messagePart contents.

.algorithm =
HashAlgorithm.SHA256
.value =
messageParts.sha256
messageSize

ocsf.email.size

The size in bytes of the
message, including
headers and attachments.

messageTime

ocsf.time

When the message was
delivered to the user or
quarantined by PPS

modulesRun

Normalized Field NA

List of PPS modules. E.g.
["av","dkimv","spf","spam",
"dmarc","pdr","urldefense"
]

phishScore

Normalized Field NA

Value from 0 - 100. Phish
score of the message.
Higher scores indicate
higher certainty.

policyRoutes

Normalized Field NA

The policy routes that the
message matched during
processing by PPS.
Example:
["default_inbound"]

QID

Normalized Field NA.
GUID can be used as

The queue ID of the
message within PPS. It

© 2025 Critical Start All rights reserved

Page 5

Proofpoint message id

can be used to identify the
message in PPS and is not
unique.

quarantineFolder

Normalized Field NA

The name of the folder
which contains the
quarantined message.
This appears only for
messagesBlocked.

quarantineRule

Normalized Field NA

he name of the rule which
quarantined the message.
This appears only for
messagesBlocked events.

recipient

ocsf.email.to

An array containing
the email addresses of the
SMTP (envelope)
recipients

replyToAddress

ocsf.email.reply_to
(Deprecated, but matches)

The email address
contained in the Reply-To:
header, excluding friendly
name.

sender

ocsf.email.http_header

The email address of the
SMTP (envelope) sender.
The user-part is hashed.
The domain-part is
cleartext.

Add HttpHeader
name = “Sender”
value = sender
senderIP

ocsf.email.x_originating_ip

spamScore

subject

Normalized Field NA

ocsf.email.subject

The IP address of the
sender.

threatsInfoMap/actors

OSINT does not have
Actors

threatInfoMap/campai
gnId

Normalized Field NA

© 2025 Critical Start All rights reserved

Value from 0 - 100. Spam
score of the message.
Higher scores indicate
higher certainty.

Example: ["@{id=
<some_guid>;
name=TA2726;
type=ACTOR}"]

Page 6

threatsInfoMap/classifi ocsf.osint.category
cation

Normalized Field NA

Malware, Phish, Spam,
etc.

threatInfoMap/detectio
nType

Normalized Field NA

Example:
COMPROMISED_WEBSITE

threatsInfoMap/threat

ocsf.osint[].value

The malicious URL, hash
of the attachment threat,
or email address of the
impostor sender.

threatsInfoMap/threatI ocsf.osint[].uid
D
threatsInfoMap/threat
Status

Normalized Field NA

active, false positive,
cleared

threatsInfoMap/threat
Time

Normalized Field NA

Proofpoint assigned the
threatStatus at this time.

threatsInfoMap/threat
Type

ocsf.osint[].type_id

threatsInfoMap/threat
Url

ocsf.osint[].src_url

toAddresses

ocsf.email.to

xmailer

ocsf.email.http_headers

Attachment, URL,
Message

Add HttpHeader
name = “X-Mailer”
value = xmailer
{threatsInfoMap.classi ioc.external_id
fication1}.
(Event.ioc.external_id)
{threatInfoMap.classifi
cation2}…
{threatInfoMap.classifi
cation_n}.
{threatsInfoMap.threat
Type1}…
{threatInfoMap.threatT
ype_n}

Since there can be n
threats in a singe event,
use a unique set of
classifications and threat
types. First include dot
separated unique
classifications in
alphabetical order
followed by unique threat
types in alphabetical
order.

Suspicious Email
ioc.external_name
Detected (Event.ioc.external_name)
{threatInfoMap.classifi
© 2025 Critical Start All rights reserved

Page 7

cation1,
threatInfoMap.classific
ation2,…
threatInfoMap.classific
ation_n} via
{threatInfoMap.threatT
ype1,
threatInfoMap.threatTy
pe2,…
threatInfoMap.threatTy
pe_n}
False

ioc.has_ioc_definition
(ioc.has_ioc_definition)

None

vendor_item_ref

None

vendor_group_ref

messageTime

event_timestamp

We should create IOC
Templates because they
never have a product
defintion.

OCSF Schema Mapping for Clicks
Source: ( Click Events defined in the API)

Field

OCSF Mapping (Network Ignored Reason
Activity)

Example Value

If Blocked:

Derived from the field
name

ocsf.action =
Action.DENIED
ocsf.disposition =
Disposition.BLOCKED
If Permitted:
ocsf.action =
Action.ALLOWED
ocsf.disposition =
Disposition.ALLOWED
ocsf.category = Network
Activity

© 2025 Critical Start All rights reserved

Page 8

ocsf.class = Network
Activity
campaignId
classification

Normalized Field NA
ocsf.osint[].category and
ocsf.url.category_ids

The threat category of the
malicious URL. E.g.
Malware, Phish, Spam.

if Malware:
.category_ids = 99, value
= Malware
if Phish:
.category_ids = 18, value =
Phishing
if Spam:
.category_ids =101, value
= Spam
id

Normalized field NA

The unique id of the click.

clickIP

ocsf.src_endpoint.ip

The external IP address of
the user who clicked on
the link. If the user is
behind a firewall
performing network
address translation, the IP
address of the firewall will
be shown.

clickTime

ocsf.time

The time the user clicked
on the URL

GUID

ocsf.osint[].email.uid

The ID of the message
within PPS. It can be used
to identify the message in
PPS and is guaranteed to
be unique.

recipient

ocsf.osint[].email.to

The email address of the
recipient if URL was in an
email.

sender

ocsf.osint[].email.from

The email address of the
sender. The user-part is
hashed. The domain-part
is cleartext.

© 2025 Critical Start All rights reserved

Page 9

senderIP

ocsf.osint[].email.x_origina
ting_ip

IP address of the sender

threatID

ocsf.osint[].uid

The unique identifier
associated with this threat.
It can be used to query
the forensics and
campaign endpoints.

threatTime

threatURL

Normalized Field NA

ocsf.osint[]

A link to the entry on the
TAP Dashboard for the
particular threat

.src_url = threatURL
.type_id = URL
threatStatus

Proofpoint identified the
URL as a threat at this
time.

Normalized Field NA

E.g. active, falsepositive,
cleared

url

ocsf.url.url_string AND
ocsf.osint[].value

The URL which was
clicked

userAgent

ocsf.app_name

The User-Agent header
from the
clicker's HTTP request

{classification}

ioc.external_id
(Event.ioc.external_id)

Suspicious URL click
detected - {classification}

ioc.external_name
(Event.ioc.external_name)

False

ioc.has_ioc_definition
(ioc.has_ioc_definition)

None

vendor_item_ref

None

vendor_group_ref

clickTime

event_timestamp

© 2025 Critical Start All rights reserved

Page 10

