import json
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch

import celery.exceptions
import responses
from django.test import override_settings
from django.utils import timezone

from apps.connectors.services import artifact_service
from apps.service_bus.tasks.task_compile_results import (
    ActionNotSupportedError,
    ConnectorDisabledError,
    ConnectorUnhealthyError,
    compile_results,
)
from apps.service_bus.v1.schemas.integration import (
    IntegrationActionType,
    IntegrationInvokeActionCommand,
)
from apps.tests.base import BaseTestCase
from factories import ConnectorFactory, OrganizationFactory


def get_empty_obj(id):
    return {"id": "source_" + str(id), "computerName": "test_device_" + str(id)}


def get_empty_result(id):
    return {
        "aad_id": None,
        "criticality": "unknown",
        "fqdns": [],
        "group_names": [],
        "hostname": "test_device_" + str(id),
        "ip_addresses": [],
        "last_seen": None,
        "mac_addresses": [],
        "is_internet_facing": None,
        "internet_exposure": "unknown",
        "os": {"family": "unknown", "host_type": "unknown", "name": ""},
        "owners": [],
        "source_id": "source_" + str(id),
        "source_data": get_empty_obj(id),
    }


def setup_responses(params=None):
    # Ensure the health check passes
    responses.post(
        "https://test_url.com/web/api/v2.1/users/api-token-details",
        json={
            "data": {
                "createdAt": "2023-09-24T20:00:45.021524Z",
                "expiresAt": (timezone.now() + timedelta(days=10))
                .isoformat()
                .replace("+00:00", "Z"),
            }
        },
    )
    expected_result_1 = [get_empty_obj(1)]
    expected_result_2 = [get_empty_obj(2)]
    parts = [
        "https://test_url.com/web/api/v2.1/agents?limit=100&countOnly=False&skipCount=False"
    ]
    if params:
        parts.append(params)
    responses.get(
        "&".join(parts),
        json={
            "data": expected_result_1,
            "pagination": {"nextCursor": "test cursor"},
        },
    )
    parts.append("cursor=test+cursor")
    responses.get(
        "&".join(parts),
        json={
            "data": expected_result_2,
            "pagination": {"nextCursor": None},
        },
    )


class CompileResultsTest(BaseTestCase):
    def setUp(self) -> None:
        super().setUp()

        self._patch_encryption()

        self.organization = OrganizationFactory()
        self.connector = ConnectorFactory(
            organization=self.organization,
            technology_id="sentinel_one",
            version_id="v2_1",
            last_activity_at=timezone.now(),
            enabled_actions=[
                IntegrationActionType.HOST_SYNC,
            ],
        )

    @responses.activate
    def test_compile_results_task_action_command(self):
        setup_responses()

        last_activity_at = self.connector.last_activity_at

        # Test
        task = compile_results.apply(
            args=(
                "37d34c1f-9286-4372-9ff2-2201b1c9ba51",
                "37d34c1f-9286-4372-9ff2-2201b1c9ba52",
                IntegrationInvokeActionCommand.command,
                json.dumps(
                    {
                        "integration_id": str(self.connector.id),
                        "action": "host_sync",
                    }
                ),
            )
        )

        # Verify
        art_ids, art_keys, result_type = task.get()
        self.assertEqual("Host", result_type)
        self.assertEqual(len(art_ids), 1)
        self.assertEqual(len(art_keys), 1)
        art_id = art_ids[0]
        art_key = artifact_service.convert_id_to_key(art_id)
        self.assertEqual(art_key, art_keys[0])
        devices = artifact_service.read_lines(art_key)
        self.assertEqual(len(devices), 2)
        self.assertEqual(devices[0], get_empty_result(1))
        self.assertEqual(devices[1], get_empty_result(2))

        self.connector.refresh_from_db()
        self.assertGreater(self.connector.last_activity_at, last_activity_at)

        self.assert_activity_logs(
            self.connector,
            [
                r"Fetch hosts completed. Elapsed time: \d+\.\d+ seconds; Items: 2; Correlation ID: [a-z0-9]+;",
                r"Fetch hosts started. Correlation ID: [a-z0-9]+;",
                "Passed health check 'Connection is valid'",
            ],
        )

    @patch(
        "apps.connectors.services.connector_service.connector_service.is_action_healthy",
        return_value=True,
    )
    @responses.activate
    def test_compile_results_task_action_command_result_type(self, m_is_action_healthy):
        from apps.connectors.tests.integrations.qualys_vmpc.qualys_vmpc_v2_0 import (
            setup_list_groups,
            setup_list_hosts_with_groups,
        )

        setup_list_hosts_with_groups()
        setup_list_groups()

        connector = ConnectorFactory(
            organization=self.organization,
            technology_id="qualys_vmpc",
            version_id="v2_0",
            last_activity_at=timezone.now(),
            enabled_actions=[
                IntegrationActionType.HOST_SYNC,
            ],
        )

        # Test
        task = compile_results.apply(
            args=(
                "37d34c1f-9286-4372-9ff2-2201b1c9ba51",
                "37d34c1f-9286-4372-9ff2-2201b1c9ba52",
                IntegrationInvokeActionCommand.command,
                json.dumps(
                    {
                        "integration_id": str(connector.id),
                        "action": "host_sync",
                    }
                ),
            )
        )

        # Verify
        art_ids, art_keys, result_type = task.get()
        self.assertEqual(result_type, "Host")
        self.assertEqual(len(art_ids), 1)
        self.assertEqual(len(art_keys), 1)

    @patch(
        "apps.connectors.services.connector_service.connector_service.is_action_healthy",
        return_value=False,
    )
    @responses.activate
    def test_compile_results_task_action_command_action_check_unhealthy(
        self, m_is_action_healthy
    ):
        from apps.connectors.tests.integrations.qualys_vmpc.qualys_vmpc_v2_0 import (
            setup_list_groups,
            setup_list_hosts_with_groups,
        )

        setup_list_hosts_with_groups()
        setup_list_groups()

        connector = ConnectorFactory(
            organization=self.organization,
            technology_id="qualys_vmpc",
            version_id="v2_0",
            last_activity_at=timezone.now(),
            enabled_actions=[
                IntegrationActionType.HOST_SYNC,
            ],
        )

        # Test
        task = compile_results.apply(
            args=(
                "37d34c1f-9286-4372-9ff2-2201b1c9ba51",
                "37d34c1f-9286-4372-9ff2-2201b1c9ba52",
                IntegrationInvokeActionCommand.command,
                json.dumps(
                    {
                        "integration_id": str(connector.id),
                        "action": "host_sync",
                    }
                ),
            )
        )

        # Verify
        with self.assertRaises(ConnectorUnhealthyError):
            task.get()

    @responses.activate
    def test_compile_results_task_connector_bad_creds(self):
        # Ensure the health check fails with unauthorized
        responses.post(
            "https://test_url.com/web/api/v2.1/users/api-token-details",
            status=401,
        )

        # Test

        with self.assertNoLogs("core.celery", level="ERROR"), self.assertLogs(
            "apps.service_bus.tasks.task_compile_results", level="WARNING"
        ):
            task = compile_results.apply(
                args=(
                    "37d34c1f-9286-4372-9ff2-2201b1c9ba51",
                    "37d34c1f-9286-4372-9ff2-2201b1c9ba52",
                    IntegrationInvokeActionCommand.command,
                    json.dumps(
                        {
                            "integration_id": str(self.connector.id),
                            "action": IntegrationActionType.HOST_SYNC,
                            "action_args": {},
                        }
                    ),
                )
            )

            # Verify
            with self.assertRaises(ConnectorUnhealthyError):
                task.get()

    @responses.activate
    def test_compile_results_task_connector_disabled(self):
        self.connector.enabled = False
        self.connector.save()

        # Test

        with self.assertNoLogs("core.celery", level="ERROR"), self.assertLogs(
            "apps.service_bus.tasks.task_compile_results", level="WARNING"
        ):
            task = compile_results.apply(
                args=(
                    "37d34c1f-9286-4372-9ff2-2201b1c9ba51",
                    "37d34c1f-9286-4372-9ff2-2201b1c9ba52",
                    IntegrationInvokeActionCommand.command,
                    json.dumps(
                        {
                            "integration_id": str(self.connector.id),
                            "action": IntegrationActionType.HOST_SYNC,
                            "action_args": {},
                        }
                    ),
                )
            )

            # Verify
            with self.assertRaises(ConnectorDisabledError):
                task.get()

    @responses.activate
    def test_compile_results_task_action_command__invalid(self):
        setup_responses()

        self.connector.enabled_actions = []
        self.connector.save()

        # Test
        task = compile_results.apply(
            args=(
                "37d34c1f-9286-4372-9ff2-2201b1c9ba51",
                "37d34c1f-9286-4372-9ff2-2201b1c9ba52",
                IntegrationInvokeActionCommand.command,
                json.dumps(
                    {
                        "integration_id": str(self.connector.id),
                        "action": "host_sync",
                    }
                ),
            )
        )

        # Verify
        with self.assertRaises(ActionNotSupportedError):
            task.get()

    def assert_activity_logs(self, connector, expected: list):
        messages = [item.message for item in connector.activity_logs.all()]
        for message, expected in zip(messages, expected):
            self.assertRegex(message, expected)

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @patch(
        "apps.connectors.services.connector_service.connector_service.is_action_healthy",
        return_value=True,
    )
    @responses.activate
    def test_compile_result_recoverable_error_retries(self, m_is_action_unhealthy):
        from apps.connectors.tests.integrations.qualys_vmpc.qualys_vmpc_v2_0 import (
            setup_error_response,
        )

        params = {
            "show_asset_id": "1",
            "show_tags": "1",
            "host_metadata": "all",
            "show_cloud_tags": "1",
            "show_trurisk": "1",
            "details": "All/AGs",
            "action": "list",
        }
        setup_error_response(
            error_file="data/error_concurrency.xml", params=params, error_code=409
        )
        connector = ConnectorFactory(
            organization=self.organization,
            technology_id="qualys_vmpc",
            version_id="v2_0",
            last_activity_at=timezone.now(),
            enabled_actions=[
                IntegrationActionType.HOST_SYNC,
            ],
        )

        # Test
        task = compile_results.apply_async(
            args=(
                "37d34c1f-9286-4372-9ff2-2201b1c9ba51",
                "37d34c1f-9286-4372-9ff2-2201b1c9ba52",
                IntegrationInvokeActionCommand.command,
                json.dumps(
                    {
                        "integration_id": str(connector.id),
                        "action": "host_sync",
                    }
                ),
            )
        )
        # Assert that we tried the request 3 times
        self.assertEqual(len(responses.calls), 3)
        for call in responses.calls:
            self.assertEqual(call.response.status_code, 409)
        with self.assertRaises(celery.exceptions.MaxRetriesExceededError):
            task.get()
