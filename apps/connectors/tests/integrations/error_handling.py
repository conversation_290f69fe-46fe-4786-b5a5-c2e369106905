from unittest import TestCase

import requests
from requests.exceptions import HTT<PERSON><PERSON><PERSON><PERSON>, JSONDecodeError

from apps.connectors.integrations.error_handling import (
    RecoverableError,
    RetryOn,
    RetrySettings,
)


class RecoverableErrorHandlerTests(TestCase):
    def fake_http_error(self, status_code=500):
        error = HTTPError()
        error.response = requests.Response()
        error.response.status_code = status_code
        return error

    def test_match_same_type(self):
        retry = RetryOn(typ=KeyError)
        self.assertTrue(retry.match(KeyError()))

    def test_match_httperror(self):
        retry = RetryOn(typ=HTTPError)
        self.assertTrue(retry.match(HTTPError()))

    def test_match_httperror_with_retry_settings(self):
        retry_settings = RetrySettings(max_retries=100)
        retry = RetryOn(typ=HTTPError, retry_settings=retry_settings)
        self.assertTrue(retry.match(HTTPError()))

    def test_match_httperror_with_status_code(self):
        retry = RetryOn(typ=HTTPError, status_code=[429])
        error = self.fake_http_error(status_code=429)
        self.assertTrue(retry.match(error))

    def test_no_match_wrong_status_code(self):
        retry = RetryOn(typ=HTTPError, status_code=[404])
        error = self.fake_http_error(status_code=500)
        self.assertFalse(retry.match(error))

    def test_no_match_different_kind(self):
        retry = RetryOn(typ=JSONDecodeError)
        self.assertFalse(retry.match(HTTPError()))

    def test_is_recoverable_error_match(self):
        error_patterns = [
            RetryOn(typ=HTTPError, status_code=[429, 500]),
            RetryOn(typ=JSONDecodeError),
        ]
        error = self.fake_http_error(status_code=429)
        match = RecoverableError.is_recoverable_error(error, error_patterns)
        self.assertEqual(match, error_patterns[0])

    def test_raise_for_retry(self):
        retry = RetryOn(typ=HTTPError, status_code=[429])
        error = self.fake_http_error(status_code=429)
        with self.assertRaises(RecoverableError) as context:
            retry.raise_for_retry(error)
            self.assertEqual(context.exception.retry_settings.max_retries, 3)
            self.assertEqual(context.exception.retry_settings.max_retries, 3)

    def test_is_recoverable_error_no_match(self):
        error_patterns = [
            RetryOn(typ=HTTPError, status_code=[404]),
            RetryOn(typ=JSONDecodeError),
        ]
        error = self.fake_http_error(status_code=500)
        self.assertIsNone(RecoverableError.is_recoverable_error(error, error_patterns))

    def test_is_recoverable_error_empty(self):
        """Test with an empty list of recoverable error patterns."""
        error = self.fake_http_error(status_code=500)
        self.assertIsNone(RecoverableError.is_recoverable_error(error, []))
