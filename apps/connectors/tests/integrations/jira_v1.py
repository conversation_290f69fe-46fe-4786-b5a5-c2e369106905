import datetime
import io
import json
from http import HTTPStatus

import responses
from django.utils.dateparse import parse_datetime

from apps.connectors.health_checks.components.component import HealthCheckComponent
from apps.connectors.integrations import IntegrationActionType
from apps.connectors.integrations.actions.external_ticket_actions import (
    AddAttachmentArgs,
    AddCommentArgs,
    AttachmentFields,
    CommentFields,
    CreateExternalTicketArgs,
    ExternalTicketFields,
    GetExternalTicketArgs,
    GetTransitionResult,
    GetTransitionsArgs,
    SearchExternalTicketArgs,
    StateUpdate,
    UpdateExternalTicketStatusArgs,
    UpdateExternalTicketStatusResult,
)
from apps.connectors.integrations.actions.update_lifecycle_status import (
    CorrIncidentStatus,
)
from apps.connectors.integrations.health_check import IntegrationHealthCheckResult
from apps.connectors.integrations.vendors.atlassian.jira.utils import (
    convert_markdown_to_adf,
    get_issue_fields,
)
from apps.connectors.integrations.vendors.atlassian.jira.v1.health_check import (
    ConnectionHealthCheck,
    StatusHealthCheck,
)
from apps.connectors.integrations.vendors.atlassian.jira.v1.settings import (
    JiraCreateIssueSettings,
)
from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
    HealthCheckComponentTestMixin,
)
from apps.tests.base import BaseTestCase
from factories import ConnectorFactory


def load_json_data(file_name):
    path = f"apps/connectors/tests/integrations/data/jira"
    with open(f"{path}/{file_name}", "r") as file:
        return json.loads(file.read())


def setup_responses():
    # Mock responses for to get Jira issue
    responses.add(
        responses.GET,
        "https://test_url.com/rest/api/3/issue/JSMTEST-14",
        json=load_json_data("get_jira_issue.json"),
        status=200,
    )

    responses.add(
        responses.GET,
        "https://test_url.com/rest/api/3/issue/INVALID-123",
        status=404,
    )

    # Mock response for creating a Jira issue
    responses.add(
        responses.POST,
        "https://test_url.com/rest/api/3/issue",
        json={
            "id": "10002",
            "key": "JSMTEST-15",
        },
        status=201,
    )

    # Mock response for adding an attachment to a Jira issue
    responses.add(
        responses.POST,
        "https://test_url.com/rest/api/3/issue/JSMTEST-12/attachments",
        json=load_json_data("add_json_attachment.json"),
        status=200,
    )

    # Mock response for fetching Jira issue transitions
    responses.add(
        responses.GET,
        "https://test_url.com/rest/api/3/issue/JSMTEST-13/transitions",
        json=load_json_data("get_jira_issue_transitions.json"),
        status=200,
    )

    # Mock response for updating Jira issue status
    responses.add(
        responses.POST,
        "https://test_url.com/rest/api/3/issue/JSMTEST-13/transitions",
        status=204,
    )

    responses.add(
        responses.GET,
        "https://test_url.com/rest/api/3/issue/INVALID-123/transitions",
        status=404,
    )

    # Mock response for adding a comment to a Jira issue
    responses.add(
        responses.POST,
        "https://test_url.com/rest/api/3/issue/TEST-123/comment",
        json=load_json_data("create_comment.json"),
        status=201,
    )

    responses.add(
        responses.POST,
        "https://test_url.com/rest/api/3/issue/INVALID-123/comment",
        status=401,
    )

    # Mock response for searching Jira issues in bulk
    responses.add(
        responses.GET,
        "https://test_url.com/rest/api/3/search/jql",
        json=load_json_data("get_bulk_issues.json"),
        status=200,
    )

    # Mock response to get all statuses
    responses.add(
        responses.GET,
        "https://test_url.com/rest/api/3/status",
        json=load_json_data("all_statuses.json"),
        status=200,
    )

    # Mock response to get attachment content
    responses.add(
        responses.GET,
        "https://test_url.com/rest/api/3/attachment/content/10066",
        body=b"Test attachment content",
        status=200,
    )


def setup_connection_data(fail: bool = False):
    response = {"error": True, "errors": {}}
    if not fail:
        response = load_json_data("get_jira_issue.json")

    responses.get(
        "https://test_base_url.com/rest/api/3/search/jql?jql=ORDER+BY+created+DESC&maxResults=1&fields=statusCategory%2Ccreated%2Ccreator%2Cupdated%2Cattachment%2Ccomment%2Cstatuscategorychangedate",
        json=response,
        status=HTTPStatus.BAD_REQUEST if fail else HTTPStatus.OK,
    )


def setup_status_data(fail: bool = False, misconfigured: bool = False):
    if misconfigured:
        response = {}

    elif fail:
        response = {"error": True, "errors": {}}

    else:
        response = load_json_data("all_statuses.json")

    responses.get(
        "https://test_base_url.com/rest/api/3/status",
        json=response,
        status=HTTPStatus.BAD_REQUEST if fail else HTTPStatus.OK,
    )


class JiraV1ApiTest(BaseTestCase):
    def setUp(self) -> None:
        super().setUp()
        setup_responses()
        self.create_ticket_settings = JiraCreateIssueSettings(
            project_key="JSMTEST",
            issue_type="10004",
            alert_type="customfield_10124",
            soc_priority="priority.name",
            alert_category="customfield_10093",
        )

        self.integration = ConnectorFactory.get_integration(
            technology_id="jira",
            config__base_url="https://test_url.com",
            version_id="v1",
        )

    @responses.activate
    def test_get_issue(self):
        api = self.integration.get_api()
        response = api.get_issue(issue_id_or_key="JSMTEST-14")
        self.assertIn("fields", response)
        self.assertEqual(response["id"], "10165")
        self.assertEqual(response["fields"]["statusCategory"]["name"], "Done")

    @responses.activate
    def test_create_issue(self):
        alert_fields = CreateExternalTicketArgs(
            alert_id="alert123",
            alert_title="Test New Issue - Response",
            alert_organization_name="Test Org",
            created_at="2025-06-27T08:15:56.990-0500",
            assigned_user_display="test_user",
            corr_status=CorrIncidentStatus("assigned"),
            alert_category="Malware",
            alert_type="Task",
            alert_link="https://example.com/alert123",
            assigned_group="Security Team",
            integration_name="MDR",
            soc_priority="Low",
            vendor_severity="Low",
        )
        issue_fields = get_issue_fields(
            alert_fields=alert_fields.model_dump(),
            field_settings=self.create_ticket_settings.model_dump(),
            project_key=self.create_ticket_settings.project_key,
            issue_type=self.create_ticket_settings.issue_type,
        )

        api = self.integration.get_api()
        response = api.create_issue(issue_fields)
        self.assertEqual({"key": "JSMTEST-15", "id": "10002"}, response)

    @responses.activate
    def test_update_issue_status(self):
        api = self.integration.get_api()
        response = api.update_issue_status(
            issue_id_or_key="JSMTEST-13", status="Mark as Done"
        )
        self.assertEqual(response.status_code, 204)

        # Test with an invalid status
        with self.assertRaises(ValueError):
            api.update_issue_status(issue_id_or_key="JSMTEST-13", status="Not Done")

    @responses.activate
    def test_add_attachment(self):
        api = self.integration.get_api()

        attachment_data = {
            "title": "Test Attachment",
            "assigned_to": "user1",
            "status": "open",
            "datetime_created": "2023-10-01T12:00:00Z",
            "vendor_severity": "low",
        }
        content = io.BytesIO(json.dumps(attachment_data, indent=2).encode("utf-8"))

        response = api.add_attachment_to_issue(
            issue_id_or_key="JSMTEST-12", file_name="EventData.json", content=content
        )
        self.assertEqual(response["id"], "10066")

    @responses.activate
    def test_get_attachment_content(self):
        api = self.integration.get_api()
        response = api.get_attachment_content(attachment_id="10066")
        self.assertEqual(response, b"Test attachment content")

    @responses.activate
    def test_add_comment_to_issue(self):
        api = self.integration.get_api()

        comment = "## This is a test markdown comment."
        comment_body = convert_markdown_to_adf(comment, "Test User", "<EMAIL>")
        response = api.add_comment_to_issue(
            issue_id_or_key="TEST-123",
            comment_body=comment_body,
        )
        self.assertEqual(response["id"], "10166")

    @responses.activate
    def test_search_issues_bulk(self):
        api = self.integration.get_api()
        jql = (
            f'project = "JSMTEST" reporter = "cs-dev" '
            f'AND updatedDate >= "2025-06-27T08:15:56.990-0500" '
            f"ORDER BY createdDate DESC"
        )
        response = api.search_issues_bulk(
            jql=jql,
            max_results=10,
        )
        self.assertEqual(response, load_json_data("get_bulk_issues.json")["issues"])

    @responses.activate
    def test_get_issue_transitions(self):
        api = self.integration.get_api()
        response = api.get_all_statuses()
        self.assertEqual(
            response,
            {
                "1": "Open",
                "3": "In Progress",
                "6": "Closed",
                "10010": "To Do",
                "10012": "Mark as Done",
                "10013": "Awaiting Vendor",
            },
        )


class JiraV1IntegrationTest(BaseIntegrationTest):
    technology_id = "jira"
    version_id = "v1"

    def setUp(self) -> None:
        super().setUp()
        setup_responses()
        self.settings = {
            "create_external_ticket": {
                "project_key": "JSMTEST",
                "issue_type": "10004",
                "alert_type": "customfield_10124",
                "soc_priority": "priority.name",
                "alert_category": "customfield_10093",
            },
            "update_external_ticket_status": {
                "status_mapping_assigned_to_customer_org": "To Do",
                "status_mapping_assigned_to_customer_org_user": "In Progress",
                "status_mapping_assigned_to_monitoring_org": "Awaiting Vendor",
                "status_mapping_closed": "Mark as Done",
            },
            "search_external_ticket": {
                "project_key": "JSMTEST",
                "user_email": "<EMAIL>",
                "status_mapping_assigned_to_customer_org": "To Do",
                "status_mapping_assigned_to_customer_org_user": "In Progress",
                "status_mapping_assigned_to_monitoring_org": "Awaiting Vendor",
                "status_mapping_closed": "Mark as Done",
            },
        }
        self.integration = ConnectorFactory.get_integration(
            technology_id="jira",
            config__base_url="https://test_url.com",
            settings=self.settings,
            version_id="v1",
        )

    @responses.activate
    def test_get_issue(self):
        args = GetExternalTicketArgs(ticket_id="JSMTEST-14")
        result = self.integration.invoke_action(
            IntegrationActionType.GET_EXTERNAL_TICKET, args
        )
        load_json_data("get_jira_issue.json")
        self.assertEqual(isinstance(result, ExternalTicketFields), True)
        self.assertEqual(result.ticket_id, "10165")
        self.assertEqual(result.ticket_key, "JSMTEST-14")
        self.assertEqual(
            result.attachments,
            [
                AttachmentFields(
                    attachment_id="10066",
                    created_at=datetime.datetime(
                        2025,
                        6,
                        30,
                        23,
                        52,
                        1,
                        67000,
                        tzinfo=datetime.timezone(
                            datetime.timedelta(days=-1, seconds=68400)
                        ),
                    ),
                    file_name="EventData.json",
                    file_content=b"Test attachment content",
                )
            ],
        )

    @responses.activate
    def test_create_issue(self):
        args = CreateExternalTicketArgs(
            alert_id="alert123",
            alert_title="Test New Issue - Response",
            alert_organization_name="Test Org",
            created_at="2025-06-27T08:15:56.990-0500",
            assigned_user_display="test_user",
            corr_status=CorrIncidentStatus("assigned"),
            alert_category="Malware",
            alert_type="Task",
            alert_link="https://example.com/alert123",
            assigned_group="Security Team",
            integration_name="MDR",
            soc_priority="Low",
            vendor_severity="Low",
        )
        result = self.integration.invoke_action(
            IntegrationActionType.CREATE_EXTERNAL_TICKET, args
        )

        self.assertEqual(result.ticket_id, "10002")
        self.assertEqual(result.ticket_key, "JSMTEST-15")

    @responses.activate
    def test_get_issue_transitions(self):
        args = GetTransitionsArgs(ticket_id="JSMTEST-13")
        result = self.integration.invoke_action(
            IntegrationActionType.GET_EXTERNAL_TICKET_TRANSITIONS, args
        )
        expected_response = load_json_data("get_jira_issue_transitions.json")
        self.assertEqual(
            result,
            GetTransitionResult(transitions=expected_response["transitions"]),
        )

    @responses.activate
    def test_update_issue_status(self):
        states = [
            StateUpdate.CLOSED,
            StateUpdate.ASSIGNED_TO_CUSTOMER_ORG,
            StateUpdate.ASSIGNED_TO_CUSTOMER_ORG_USER,
            StateUpdate.ASSIGNED_TO_MONITORING_ORG,
        ]
        for state in states:
            args = UpdateExternalTicketStatusArgs(ticket_id="JSMTEST-13", state=state)
            result = self.integration.invoke_action(
                IntegrationActionType.UPDATE_EXTERNAL_TICKET_STATUS, args
            )
            self.assertEqual(result, UpdateExternalTicketStatusResult())

        # Test with an invalid status
        settings = self.settings
        settings["update_external_ticket_status"] = {
            "status_mapping_assigned_to_customer_org": "New",
            "status_mapping_assigned_to_customer_org_user": "In Progress",
        }
        integration = ConnectorFactory.get_integration(
            technology_id="jira",
            config__base_url="https://test_url.com",
            settings=settings,
            version_id="v1",
        )
        args = UpdateExternalTicketStatusArgs(
            ticket_id="JSMTEST-13",
            state=StateUpdate.ASSIGNED_TO_CUSTOMER_ORG,
            settings=settings,
        )
        with self.assertRaises(ValueError):
            integration.invoke_action(
                IntegrationActionType.UPDATE_EXTERNAL_TICKET_STATUS, args
            )

    @responses.activate
    def test_add_attachment(self):
        attachment_data = {
            "title": "Test Attachment",
            "assigned_to": "user1",
            "status": "open",
            "datetime_created": "2023-10-01T12:00:00Z",
            "vendor_severity": "low",
        }
        file_content = json.dumps(attachment_data, indent=2).encode("utf-8")
        args = AddAttachmentArgs(
            ticket_id="JSMTEST-12",
            file_name="EventData.json",
            content=file_content,
        )
        result = self.integration.invoke_action(
            IntegrationActionType.ADD_ATTACHMENT_TO_EXTERNAL_TICKET, args
        )
        self.assertEqual(result.attachment_id, "10066")
        self.assertEqual(result.file_name, "EventData.json")
        self.assertEqual(result.author_display_name, "CS Dev")
        self.assertEqual(result.author_email, "<EMAIL>")

    @responses.activate
    def test_add_comment_to_issue(self):
        comment = "## This is a test markdown comment."
        args = AddCommentArgs(
            ticket_id="TEST-123",
            author_display_name="test_user",
            author_email="<EMAIL>",
            comment=comment,
        )
        result = self.integration.invoke_action(
            IntegrationActionType.ADD_COMMENT_TO_EXTERNAL_TICKET, args
        )
        self.assertEqual(result.comment_id, "10166")

    @responses.activate
    def test_search_issues_bulk(self):
        args = SearchExternalTicketArgs(updated_at="2025-06-27T08:15:56.990-0500")
        result = self.integration.invoke_action(
            IntegrationActionType.SEARCH_EXTERNAL_TICKET, args
        )
        result_fields = result.search_result[0]

        comments = [
            CommentFields(
                comment_id="10075",
                created_at=datetime.datetime(
                    2025,
                    6,
                    23,
                    22,
                    15,
                    43,
                    567000,
                    tzinfo=datetime.timezone(
                        datetime.timedelta(days=-1, seconds=68400)
                    ),
                ),
                updated_at=datetime.datetime(
                    2025,
                    6,
                    23,
                    22,
                    15,
                    43,
                    567000,
                    tzinfo=datetime.timezone(
                        datetime.timedelta(days=-1, seconds=68400)
                    ),
                ),
                author_display_name="CS Dev",
                author_email="<EMAIL>",
                comment_md="# What Occurrred\n\nMicrosoft Sentinel reported `CS - Linux - Multiple SSH Failed Password Events` indicating potential malicious activity related to failed SSH login attempts on a Linux system.\n\n# Still Occurring\n\nThe activity was detected and reported by `Microsoft` Sentinel, but it is `unknown` if the activity was blocked or prevented.\n\n# Risks\n\nThe event points to potential unauthorized access attempts on a Linux system, which could lead to a successful compromise of sensitive data or resources.\n\nThe `low` severity of the alert may indicate that the malicious activity is still ongoing or has not been effectively mitigated.\n\n# Recommendations\n\nInvestigate the failed SSH login attempts on the Linux system to determine the extent of the unauthorized access attempts.\n\nReview the `account` `user1` for any signs of compromise or suspicious activity.\n\nEnsure that appropriate security measures are in place to prevent further unauthorized access attempts.\n\n*(Comment added by ZTAP System User.)*",
                attachment_filenames=[],
            ),
            CommentFields(
                comment_id="10076",
                created_at=datetime.datetime(
                    2025,
                    6,
                    23,
                    22,
                    30,
                    25,
                    305000,
                    tzinfo=datetime.timezone(
                        datetime.timedelta(days=-1, seconds=68400)
                    ),
                ),
                updated_at=datetime.datetime(
                    2025,
                    6,
                    23,
                    22,
                    30,
                    25,
                    305000,
                    tzinfo=datetime.timezone(
                        datetime.timedelta(days=-1, seconds=68400)
                    ),
                ),
                author_display_name="CS Dev",
                author_email="<EMAIL>",
                comment_md="Safe\n\n*(Comment added by ZTAP System User.)*",
                attachment_filenames=[],
            ),
            CommentFields(
                comment_id="10165",
                created_at=datetime.datetime(
                    2025,
                    6,
                    27,
                    8,
                    15,
                    13,
                    729000,
                    tzinfo=datetime.timezone(
                        datetime.timedelta(days=-1, seconds=68400)
                    ),
                ),
                updated_at=datetime.datetime(
                    2025,
                    6,
                    27,
                    8,
                    15,
                    13,
                    729000,
                    tzinfo=datetime.timezone(
                        datetime.timedelta(days=-1, seconds=68400)
                    ),
                ),
                author_display_name="CS Dev",
                author_email="<EMAIL>",
                comment_md="Safe\n\n*(Comment added by ZTAP System User.)*",
                attachment_filenames=[],
            ),
        ]
        attachment = [
            AttachmentFields(
                attachment_id="10066",
                created_at=datetime.datetime(
                    2025,
                    6,
                    30,
                    23,
                    52,
                    1,
                    67000,
                    tzinfo=datetime.timezone(
                        datetime.timedelta(days=-1, seconds=68400)
                    ),
                ),
                file_name="EventData.json",
                file_content=b"Test attachment content",
            )
        ]

        self.assertEqual(len(result.search_result), 1)
        self.assertEqual(result_fields.ticket_id, "10100")
        self.assertEqual(result_fields.ticket_key, "JSMTEST-8")
        self.assertEqual(
            result_fields.created_at, parse_datetime("2025-06-23T20:52:54.813-0500")
        )
        self.assertEqual(result_fields.created_by_display_name, "CS Dev")
        self.assertEqual(result_fields.created_by_email, "<EMAIL>")
        self.assertEqual(result_fields.ticket_status, "To Do")
        self.assertEqual(
            result_fields.ticket_state, StateUpdate.ASSIGNED_TO_CUSTOMER_ORG
        )
        self.assertEqual(
            result_fields.status_updated_at,
            parse_datetime("2025-06-27T08:15:16.477-0500"),
        )
        self.assertEqual(
            result_fields.status_change_date,
            parse_datetime("2025-06-27T08:15:16.477-0500"),
        )
        self.assertEqual(result_fields.comments, comments)
        self.assertEqual(result_fields.attachments, attachment)


class JiraV1HealthCheckComponentsTest(BaseTestCase, HealthCheckComponentTestMixin):
    def setUp(self) -> None:
        super().setUp()
        self._patch_encryption()
        self.connector = ConnectorFactory(
            technology_id="jira",
            enabled_actions=[
                IntegrationActionType.GET_EXTERNAL_TICKET,
                IntegrationActionType.CREATE_EXTERNAL_TICKET,
                IntegrationActionType.GET_EXTERNAL_TICKET_TRANSITIONS,
                IntegrationActionType.UPDATE_EXTERNAL_TICKET_STATUS,
                IntegrationActionType.ADD_ATTACHMENT_TO_EXTERNAL_TICKET,
                IntegrationActionType.ADD_COMMENT_TO_EXTERNAL_TICKET,
                IntegrationActionType.SEARCH_EXTERNAL_TICKET,
            ],
            version_id="v1",
            settings={},
            config__base_url="https://test_url.com",
        )
        self.integration = self.connector.get_integration(decrypt_config=False)

    @responses.activate
    def test_components(self):
        components = HealthCheckComponent.get_components(connector=self.connector)

        self.assert_components(components, [])


class JiraV1HealthCheckTest(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.settings = {
            "search_external_ticket": {
                "project_key": "JSMTEST",
                "user_email": "<EMAIL>",
                "status_mapping_assigned_to_customer_org": "To Do",
                "status_mapping_assigned_to_customer_org_user": "In Progress",
                "status_mapping_assigned_to_monitoring_org": "Awaiting Vendor",
                "status_mapping_closed": "Done",
            },
        }
        self.integration = ConnectorFactory.get_integration(
            technology_id="jira",
            version_id="v1",
            settings=self.settings,
        )

    @responses.activate
    def test_health_check(self):
        setup_connection_data(fail=False)
        health_check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_health_check_fail(self):
        setup_connection_data(fail=True)
        health_check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)

    @responses.activate
    def test_status_health_check(self):
        setup_status_data(fail=False)
        health_check = StatusHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_status_health_check_fail(self):
        setup_status_data(fail=True)
        health_check = StatusHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)

    @responses.activate
    def test_status_health_check_misconfigured(self):
        setup_status_data(misconfigured=True)
        health_check = StatusHealthCheck(integration=self.integration)
        self.assertEqual(
            health_check.get_result(), IntegrationHealthCheckResult.MISCONFIGURED
        )
