{"expand": "renderedFields,names,schema,operations,editmeta,changelog,versionedRepresentations,customfield_10010.requestTypePractice", "id": "10165", "self": "https://cs-dev.atlassian.net/rest/api/3/issue/10165", "key": "JSMTEST-14", "fields": {"statuscategorychangedate": "2025-06-26T11:17:28.020-0500", "issuetype": {"self": "https://cs-dev.atlassian.net/rest/api/3/issuetype/10004", "id": "10004", "description": "A task that needs to be done.", "iconUrl": "https://cs-dev.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10699?size=medium", "name": "Task", "subtask": false, "avatarId": 10699, "hierarchyLevel": 0}, "timespent": null, "customfield_10030": null, "project": {"self": "https://cs-dev.atlassian.net/rest/api/3/project/10000", "id": "10000", "key": "JSMTEST", "name": "JSMTEST1", "projectTypeKey": "service_desk", "simplified": false}, "fixVersions": [], "statusCategory": {"self": "https://cs-dev.atlassian.net/rest/api/3/statuscategory/3", "id": 3, "key": "done", "colorName": "green", "name": "Done"}, "workratio": -1, "lastViewed": "2025-06-26T14:09:42.573-0500", "watches": {"self": "https://cs-dev.atlassian.net/rest/api/3/issue/JSMTEST-14/watchers", "watchCount": 1, "isWatching": true}, "created": "2025-06-26T10:21:20.741-0500", "customfield_10067": {"id": "1", "name": "Time to resolution", "_links": {"self": "https://cs-dev.atlassian.net/rest/servicedeskapi/request/10165/sla/1"}, "completedCycles": []}, "priority": {"self": "https://cs-dev.atlassian.net/rest/api/3/priority/4", "iconUrl": "https://cs-dev.atlassian.net/images/icons/priorities/low_new.svg", "name": "Low", "id": "4"}, "customfield_10068": {"id": "2", "name": "Time to first response", "_links": {"self": "https://cs-dev.atlassian.net/rest/servicedeskapi/request/10165/sla/2"}, "completedCycles": []}, "customfield_10069": {"id": "3", "name": "Time to close after resolution", "_links": {"self": "https://cs-dev.atlassian.net/rest/servicedeskapi/request/10165/sla/3"}, "completedCycles": []}, "customfield_10018": {"hasEpicLinkFieldDependency": false, "showField": false, "nonEditableReason": {"reason": "PLUGIN_LICENSE_ERROR", "message": "The Parent Link is only available to Jira Premium users."}}, "customfield_10019": "0|i0001z:", "versions": [], "issuelinks": [], "assignee": null, "updated": "2025-06-26T14:18:43.656-0500", "status": {"self": "https://cs-dev.atlassian.net/rest/api/3/status/10008", "description": "This was auto-generated by Jira Service Management during workflow import", "iconUrl": "https://cs-dev.atlassian.net/images/icons/status_generic.gif", "name": "Done", "id": "10008", "statusCategory": {"self": "https://cs-dev.atlassian.net/rest/api/3/statuscategory/3", "id": 3, "key": "done", "colorName": "green", "name": "Done"}}, "components": [], "customfield_10091": "Information Security - Silver", "customfield_10092": "Malware", "customfield_10093": "L2_Global_Sec_InfoSec_MDRalerts", "timeoriginalestimate": null, "customfield_10095": "P1", "customfield_10096": "Event", "description": {"type": "doc", "version": 1, "content": [{"type": "paragraph", "content": [{"type": "text", "text": "<This is the sample body>"}]}]}, "customfield_10058": {"languageCode": "en", "displayName": "English"}, "customfield_10059": {"self": "https://cs-dev.atlassian.net/rest/api/3/customFieldOption/10023", "value": "Low", "id": "10023"}, "summary": "Test New Issue - Response", "creator": {"self": "https://cs-dev.atlassian.net/rest/api/3/user?accountId=64ab21561006977b98a", "accountId": "64ab21561006977b98a", "emailAddress": "<EMAIL>", "avatarUrls": {"48x48": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/64ab21561006977b98a/c7e06aff-6683-4357-b774-cbc9993176be/48", "24x24": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/64ab21561006977b98a/c7e06aff-6683-4357-b774-cbc9993176be/24", "16x16": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/64ab21561006977b98a/c7e06aff-6683-4357-b774-cbc9993176be/16", "32x32": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/64ab21561006977b98a/c7e06aff-6683-4357-b774-cbc9993176be/32"}, "displayName": "cs-dev", "active": true, "timeZone": "America/Chicago", "accountType": "atlassian"}, "reporter": {"self": "https://cs-dev.atlassian.net/rest/api/3/user?accountId=64ab21561006977b98a", "accountId": "64ab21561006977b98a", "emailAddress": "<EMAIL>", "avatarUrls": {"48x48": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/64ab21561006977b98a/c7e06aff-6683-4357-b774-cbc9993176be/48", "24x24": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/64ab21561006977b98a/c7e06aff-6683-4357-b774-cbc9993176be/24", "16x16": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/64ab21561006977b98a/c7e06aff-6683-4357-b774-cbc9993176be/16", "32x32": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/64ab21561006977b98a/c7e06aff-6683-4357-b774-cbc9993176be/32"}, "displayName": "cs-dev", "active": true, "timeZone": "America/Chicago", "accountType": "atlassian"}, "aggregateprogress": {"progress": 0, "total": 0}, "customfield_10124": "Incident", "customfield_10004": {"self": "https://cs-dev.atlassian.net/rest/api/3/customFieldOption/10003", "value": "Minor / Localized", "id": "10003"}, "progress": {"progress": 0, "total": 0}, "comment": {}, "attachment": [{"self": "https://cs-dev.atlassian.net/rest/api/3/attachment/10066", "id": "10066", "filename": "EventData.json", "author": {"self": "https://cs-dev.atlassian.net/rest/api/3/user?accountId=************************", "emailAddress": "<EMAIL>", "displayName": "CS Dev", "active": true, "timeZone": "America/Chicago", "accountType": "atlassian"}, "created": "2025-06-30T23:52:01.067-0500", "size": 6565, "mimeType": "application/json", "content": "https://cs-dev.atlassian.net/rest/api/3/attachment/content/10066"}]}}