{"expand": "transitions", "transitions": [{"id": "40", "name": "To Do", "to": {"self": "https://cs-dev.atlassian.net/rest/api/3/status/10002", "description": "This was auto-generated by Jira Service Management during workflow import", "iconUrl": "https://cs-dev.atlassian.net/images/icons/status_generic.gif", "name": "To Do", "id": "10001", "statusCategory": {"self": "https://cs-dev.atlassian.net/rest/api/3/statuscategory/3", "id": 4, "key": "indeterminate", "colorName": "Red", "name": "To Do"}}, "hasScreen": true, "isGlobal": false, "isInitial": false, "isAvailable": true, "isConditional": false, "isLooped": false}, {"id": "41", "name": "In Progress", "to": {"self": "https://cs-dev.atlassian.net/rest/api/3/status/10002", "description": "This was auto-generated by Jira Service Management during workflow import", "iconUrl": "https://cs-dev.atlassian.net/images/icons/status_generic.gif", "name": "In Progress", "id": "10002", "statusCategory": {"self": "https://cs-dev.atlassian.net/rest/api/3/statuscategory/4", "id": 4, "key": "indeterminate", "colorName": "yellow", "name": "In Progress"}}, "hasScreen": true, "isGlobal": false, "isInitial": false, "isAvailable": true, "isConditional": false, "isLooped": false}, {"id": "11", "name": "Awaiting <PERSON><PERSON><PERSON>", "to": {"self": "https://cs-dev.atlassian.net/rest/api/3/status/10006", "description": "This was auto-generated by Jira Service Management during workflow import", "iconUrl": "https://cs-dev.atlassian.net/images/icons/status_generic.gif", "name": "Awaiting <PERSON><PERSON><PERSON>", "id": "10006", "statusCategory": {"self": "https://cs-dev.atlassian.net/rest/api/3/statuscategory/4", "id": 4, "key": "indeterminate", "colorName": "green", "name": "Awaiting <PERSON><PERSON><PERSON>"}}, "hasScreen": true, "isGlobal": false, "isInitial": false, "isAvailable": true, "isConditional": false, "isLooped": false}, {"id": "61", "name": "<PERSON> as <PERSON>", "to": {"self": "https://cs-dev.atlassian.net/rest/api/3/status/10008", "description": "This was auto-generated by Jira Service Management during workflow import", "iconUrl": "https://cs-dev.atlassian.net/images/icons/status_generic.gif", "name": "Done", "id": "10008", "statusCategory": {"self": "https://cs-dev.atlassian.net/rest/api/3/statuscategory/3", "id": 3, "key": "done", "colorName": "green", "name": "Done"}}, "hasScreen": true, "isGlobal": false, "isInitial": false, "isAvailable": true, "isConditional": false, "isLooped": false}]}