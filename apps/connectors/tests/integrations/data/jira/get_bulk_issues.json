{"issues": [{"expand": "renderedFields,names,schema,operations,editmeta,changelog,versionedRepresentations", "id": "10100", "self": "https://cs-dev.atlassian.net/rest/api/3/issue/10100", "key": "JSMTEST-8", "fields": {"statuscategorychangedate": "2025-06-27T08:15:16.477-0500", "attachment": [{"self": "https://cs-dev.atlassian.net/rest/api/3/attachment/10103", "id": "10066", "filename": "EventData.json", "author": {"self": "https://cs-dev.atlassian.net/rest/api/3/user?accountId=************************", "accountId": "************************", "emailAddress": "<EMAIL>", "avatarUrls": {"48x48": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/c7e06aff-6683-4357-b774-cbc9993176be/48", "24x24": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/c7e06aff-6683-4357-b774-cbc9993176be/24", "16x16": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/c7e06aff-6683-4357-b774-cbc9993176be/16", "32x32": "https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/c7e06aff-6683-4357-b774-cbc9993176be/32"}, "displayName": "CS-Dev", "active": true, "timeZone": "America/Chicago", "accountType": "atlassian"}, "created": "2025-06-30T23:52:01.067-0500", "size": 6565, "mimeType": "application/json", "content": "https://cs-dev.atlassian.net/rest/api/3/attachment/content/10103"}], "creator": {"displayName": "CS Dev", "emailAddress": "<EMAIL>"}, "created": "2025-06-23T20:52:54.813-0500", "comment": {"comments": [{"self": "https://cs-dev.atlassian.net/rest/api/3/issue/10100/comment/10075", "id": "10075", "author": {"self": "https://cs-dev.atlassian.net/rest/api/3/user?accountId=************************", "accountId": "************************", "emailAddress": "<EMAIL>", "displayName": "CS Dev", "active": true, "timeZone": "America/Chicago", "accountType": "atlassian"}, "body": {"type": "doc", "version": 1, "content": [{"type": "heading", "attrs": {"level": 1}, "content": [{"type": "text", "text": "What Occurrred"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "Microsoft Sentinel reported "}, {"type": "text", "text": "CS - Linux - Multiple SSH Failed Password Events", "marks": [{"type": "code"}]}, {"type": "text", "text": " indicating potential malicious activity related to failed SSH login attempts on a Linux system."}]}, {"type": "heading", "attrs": {"level": 1}, "content": [{"type": "text", "text": "Still Occurring"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "The activity was detected and reported by "}, {"type": "text", "text": "Microsoft", "marks": [{"type": "code"}]}, {"type": "text", "text": " Sentinel, but it is "}, {"type": "text", "text": "unknown", "marks": [{"type": "code"}]}, {"type": "text", "text": " if the activity was blocked or prevented."}]}, {"type": "heading", "attrs": {"level": 1}, "content": [{"type": "text", "text": "Risks"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "The event points to potential unauthorized access attempts on a Linux system, which could lead to a successful compromise of sensitive data or resources."}]}, {"type": "paragraph", "content": [{"type": "text", "text": "The "}, {"type": "text", "text": "low", "marks": [{"type": "code"}]}, {"type": "text", "text": " severity of the alert may indicate that the malicious activity is still ongoing or has not been effectively mitigated."}]}, {"type": "heading", "attrs": {"level": 1}, "content": [{"type": "text", "text": "Recommendations"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "Investigate the failed SSH login attempts on the Linux system to determine the extent of the unauthorized access attempts."}]}, {"type": "paragraph", "content": [{"type": "text", "text": "Review the "}, {"type": "text", "text": "account", "marks": [{"type": "code"}]}, {"type": "text", "text": " "}, {"type": "text", "text": "user1", "marks": [{"type": "code"}]}, {"type": "text", "text": " for any signs of compromise or suspicious activity."}]}, {"type": "paragraph", "content": [{"type": "text", "text": "Ensure that appropriate security measures are in place to prevent further unauthorized access attempts."}]}, {"type": "paragraph", "content": [{"type": "text", "text": "(Comment added by ZTAP System User.)", "marks": [{"type": "em"}]}]}]}, "created": "2025-06-23T22:15:43.567-0500", "updated": "2025-06-23T22:15:43.567-0500", "jsdPublic": true}, {"self": "https://cs-dev.atlassian.net/rest/api/3/issue/10100/comment/10076", "id": "10076", "author": {"self": "https://cs-dev.atlassian.net/rest/api/3/user?accountId=************************", "accountId": "************************", "emailAddress": "<EMAIL>", "displayName": "CS Dev", "active": true, "timeZone": "America/Chicago", "accountType": "atlassian"}, "body": {"type": "doc", "version": 1, "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Safe"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "(Comment added by ZTAP System User.)", "marks": [{"type": "em"}]}]}]}, "created": "2025-06-23T22:30:25.305-0500", "updated": "2025-06-23T22:30:25.305-0500", "jsdPublic": true}, {"self": "https://cs-dev.atlassian.net/rest/api/3/issue/10100/comment/10165", "id": "10165", "author": {"self": "https://cs-dev.atlassian.net/rest/api/3/user?accountId=************************", "accountId": "************************", "emailAddress": "<EMAIL>", "displayName": "CS Dev", "active": true, "timeZone": "America/Chicago", "accountType": "atlassian"}, "body": {"type": "doc", "version": 1, "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Safe"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "(Comment added by ZTAP System User.)", "marks": [{"type": "em"}]}]}]}, "created": "2025-06-27T08:15:13.729-0500", "updated": "2025-06-27T08:15:13.729-0500", "jsdPublic": true}], "self": "https://cs-dev.atlassian.net/rest/api/3/issue/10100/comment", "maxResults": 3, "total": 3, "startAt": 0}, "statusCategory": {"self": "https://cs-dev.atlassian.net/rest/api/3/statuscategory/2", "id": 2, "key": "new", "colorName": "blue-gray", "name": "To Do"}, "updated": "2025-06-27T08:15:16.477-0500"}}], "isLast": true, "nextPageToken": "next_token"}