import logging
import os
import xml.etree.ElementTree as ElementTree
from copy import deepcopy
from datetime import datetime, timedelta
from unittest import mock
from unittest.mock import patch

import requests
import responses
from parameterized import parameterized
from pytz import utc
from requests import HTTPError
from responses import matchers

from apps.connectors.health_checks.components.component import (
    ComponentType,
    HealthCheckComponent,
    HealthCheckRequirement,
    RequirementStatus,
    ValidationStatus,
)
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.actions.detected_vulnerability_sync import (
    DetectedVulnerability,
    DetectedVulnerabilityConfidenceLevel,
    DetectedVulnerabilityConfigurationState,
    DetectedVulnerabilityKernelState,
    DetectedVulnerabilityServiceState,
    DetectedVulnerabilityStatus,
)
from apps.connectors.integrations.actions.host_sync import (
    AssetCriticality,
    Host,
    InternetExposure,
)
from apps.connectors.integrations.actions.vendor_vulnerability_sync import (
    VendorVulnerability,
)
from apps.connectors.integrations.health_check import IntegrationHealthCheckResult
from apps.connectors.integrations.schemas.operating_system import (
    HostType,
    OsAttributes,
    OsFamily,
)
from apps.connectors.integrations.vendors.qualys.qualys_vmpc.v2_0 import (
    QualysVmpcV20Settings,
)
from apps.connectors.integrations.vendors.qualys.qualys_vmpc.v2_0.actions.vendor_vulnerability_sync.vendor_vulnerability import (
    html_to_markdown,
)
from apps.connectors.integrations.vendors.qualys.qualys_vmpc.v2_0.api import paginate
from apps.connectors.integrations.vendors.qualys.qualys_vmpc.v2_0.health_check import (
    ConnectionHealthCheck,
    EulaAccepted,
    ReadDetections,
    ReadHosts,
    ReadKnowledgeBase,
)
from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
    HealthCheckComponentTestMixin,
)
from apps.tests.base import BaseTestCase
from factories import ConnectorFactory


def load_xml_data(filename):
    dir_path = os.path.dirname(os.path.realpath(__file__))
    file_path = os.path.join(dir_path, filename)
    tree = ElementTree.parse(file_path)
    return ElementTree.tostring(tree.getroot(), encoding="utf8", method="xml")


def setup_session_login(username=None, password=None):
    params = {
        "action": "login",
        "username": username,
        "password": password,
    }
    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/session/",
        match=[matchers.urlencoded_params_matcher(params)],
        content_type="text/xml",
        body=load_xml_data("data/session_login.xml"),
        status=200,
    )


def setup_error_response(error_file="data/error.xml", params=None, error_code=400):
    if not params:
        params = {
            "action": "list",
        }
    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/asset/host/",
        match=[matchers.urlencoded_params_matcher(params)],
        content_type="text/xml",
        body=load_xml_data(error_file),
        status=error_code,
    )


def setup_list_all_hosts(params=None, page_limit=None):
    params = {
        "action": "list",
        "show_asset_id": "1",
        "show_tags": "1",
        "host_metadata": "all",
        "show_cloud_tags": "1",
        "show_trurisk": "1",
        "details": "All",
    }
    if page_limit is not None:
        params["truncation_limit"] = str(page_limit)

    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/asset/host/",
        match=[matchers.urlencoded_params_matcher(params)],
        content_type="text/xml",
        body=load_xml_data("data/hosts_page_1.xml"),
        status=200,
    )

    params_page_2 = deepcopy(params)
    params_page_2["id_min"] = "9824981"
    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/asset/host/",
        match=[matchers.urlencoded_params_matcher(params_page_2)],
        content_type="text/xml",
        body=load_xml_data("data/hosts_page_2.xml"),
        status=200,
    )


def setup_list_hosts(params=None, page_limit=None):
    params = {
        "action": "list",
        "show_asset_id": "1",
        "use_tags": "1",
        "tag_set_by": "name",
        "tag_set_include": "tag_id_1,tag_id_2",
        "details": "All",
        "show_tags": "1",
        "show_cloud_tags": "1",
        "host_metadata": "all",
        "show_trurisk": "1",
    }
    if page_limit is not None:
        params["truncation_limit"] = str(page_limit)

    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/asset/host/",
        match=[matchers.urlencoded_params_matcher(params)],
        content_type="text/xml",
        body=load_xml_data("data/hosts_page_1.xml"),
        status=200,
    )

    params_page_2 = deepcopy(params)
    params_page_2["id_min"] = "9824981"
    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/asset/host/",
        match=[matchers.urlencoded_params_matcher(params_page_2)],
        content_type="text/xml",
        body=load_xml_data("data/hosts_page_2.xml"),
        status=200,
    )


def setup_list_hosts_for_detections():
    params = {
        "action": "list",
        "show_asset_id": "1",
        "details": "Basic",
        "show_tags": "0",
        "host_metadata": "all",
        "show_trurisk": "1",
    }

    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/asset/host/",
        match=[matchers.urlencoded_params_matcher(params)],
        content_type="text/xml",
        body=load_xml_data("data/hosts_for_detections.xml"),
        status=200,
    )


def setup_list_detected_vulnerabilities(
    page_limit=None,
):
    params = {
        "action": "list",
        "detection_updated_since": "2024-04-01T00:00:00Z",
        "show_asset_id": "1",
        "status": "New,Active,Re-Opened,Fixed",
        "include_vuln_type": "confirmed",
        "arf_kernel_filter": "0",
        "arf_service_filter": "0",
        "arf_config_filter": "0",
        "show_tags": "1",
    }
    if page_limit is not None:
        params["truncation_limit"] = str(page_limit)

    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
        match=[matchers.urlencoded_params_matcher(params)],
        content_type="text/xml",
        body=load_xml_data(f"data/detections_confirmed_page_1.xml"),
        status=200,
    )

    params_page_2 = deepcopy(params)
    params_page_2["id_min"] = "9824981"
    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
        match=[matchers.urlencoded_params_matcher(params_page_2)],
        content_type="text/xml",
        body=load_xml_data(f"data/detections_confirmed_page_2.xml"),
        status=200,
    )

    params_superseded_page_1 = deepcopy(params)
    params_superseded_page_1["filter_superseded_qids"] = "1"
    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
        match=[matchers.urlencoded_params_matcher(params_superseded_page_1)],
        content_type="text/xml",
        body=load_xml_data(f"data/detections_confirmed_superseded_page_1.xml"),
        status=200,
    )

    params_potential = deepcopy(params)
    params_potential["include_vuln_type"] = "potential"
    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
        match=[matchers.urlencoded_params_matcher(params_potential)],
        content_type="text/xml",
        body=load_xml_data(f"data/detections_potential_page_1.xml"),
        status=200,
    )
    params_potential_superseded = deepcopy(params_potential)
    params_potential_superseded["filter_superseded_qids"] = "1"
    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
        match=[matchers.urlencoded_params_matcher(params_potential_superseded)],
        content_type="text/xml",
        body=load_xml_data(f"data/detections_potential_superseded_page_1.xml"),
        status=200,
    )


def setup_list_vendor_vulnerabilities():
    params = {
        "action": "list",
        "details": "All",
        "last_modified_after": "2024-04-01T00:00:00Z",
    }

    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/knowledge_base/vuln/",
        match=[matchers.urlencoded_params_matcher(params)],
        content_type="text/xml",
        body=load_xml_data("data/knowledge_base.xml"),
        status=200,
    )


def setup_eula_accepted(already_accepted=False, fail=False):
    if fail:
        data = load_xml_data("data/eula_failed.xml")
    else:
        data = load_xml_data(
            "data/eula_already_accepted.xml"
            if already_accepted
            else "data/eula_accepted.xml"
        )

    responses.get(
        "https://qualysapi.qualys.com/msp/acceptEULA.php",
        content_type="text/xml",
        body=data,
        status=401 if fail else 200,
    )


def setup_list_hosts_with_groups(page_limit=None):
    params = {
        "action": "list",
        "show_asset_id": "1",
        "details": "All/AGs",
        "show_tags": "1",
        "show_cloud_tags": "1",
        "host_metadata": "all",
        "show_trurisk": "1",
    }
    if page_limit is not None:
        params["truncation_limit"] = str(page_limit)

    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/asset/host/",
        match=[matchers.urlencoded_params_matcher(params)],
        content_type="text/xml",
        body=load_xml_data("data/hosts_with_groups.xml"),
        status=200,
    )


def setup_list_groups(response_file="data/groups.xml"):
    params = {
        "action": "list",
        "truncation_limit": "0",
        "show_attributes": "TITLE",
        "ids": "123,456",
    }
    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/asset/group/",
        match=[matchers.urlencoded_params_matcher(params)],
        content_type="text/xml",
        body=load_xml_data(response_file),
        status=200,
    )

    # Also setup responses with different order of ids, since it's not deterministic.
    params = {
        "action": "list",
        "truncation_limit": "0",
        "show_attributes": "TITLE",
        "ids": "456,123",
    }
    responses.post(
        "https://qualysapi.qualys.com/api/2.0/fo/asset/group/",
        match=[matchers.urlencoded_params_matcher(params)],
        content_type="text/xml",
        body=load_xml_data(response_file),
        status=200,
    )


class QualysVmpcV20ApiTest(BaseTestCase):
    @responses.activate
    def test_list_hosts(self):
        setup_list_hosts(page_limit=2)

        params = {
            "truncation_limit": 2,
            "show_asset_id": 1,
            "use_tags": 1,
            "tag_set_by": "name",
            "tag_set_include": "tag_id_1,tag_id_2",
            "details": "All",
            "show_tags": "1",
            "show_cloud_tags": 1,
            "host_metadata": "all",
            "show_trurisk": 1,
        }
        api = ConnectorFactory.get_api(technology_id="qualys_vmpc", version_id="v2_0")
        result = api.list_hosts(**params)
        expected = [
            {
                "ID": "9739698",
                "TAGS": {
                    "TAG": [
                        {"TAG_ID": "tag_id_1", "NAME": "Tag1"},
                        {"TAG_ID": "tag_id_2", "NAME": "Internet Facing Assets"},
                    ]
                },
                "LAST_VM_SCANNED_DATE": "2024-01-07T21:12:33Z",
                "DNS_DATA": {
                    "HOSTNAME": "host1",
                    "FQDN": "host1.example.com",
                },
                "IP": "***********",
                "OS": "Windows 10",
                "ASSET_CRITICALITY_SCORE": "2",
            },
            {
                "ID": "9824979",
                "TAGS": {"TAG": [{"TAG_ID": "tag_id_1", "NAME": "Tag1"}]},
                "LAST_VM_SCANNED_DATE": "2024-01-09T20:01:26Z",
                "DNS_DATA": {
                    "HOSTNAME": "host2",
                    "FQDN": "host2.example.com",
                },
                "IP": "***********",
                "OS": "Windows Server 2019",
            },
        ], "9824981"
        self.assertEqual(expected, result)

    @responses.activate
    def test_list_groups(self):
        setup_list_groups()

        api = ConnectorFactory.get_api(technology_id="qualys_vmpc", version_id="v2_0")
        groups = api.list_groups(
            ids=["123,456"], show_attributes="TITLE", truncation_limit=0
        )
        expected = [
            {
                "ID": "123",
                "TITLE": "Asset Group 123",
            },
            {
                "ID": "456",
                "TITLE": "Asset Group 456",
            },
        ], None
        self.assertEqual(
            expected,
            groups,
        )

    @responses.activate
    def test_list_detected_vulnerabilities(self):
        setup_list_detected_vulnerabilities(page_limit=2)

        params = {
            "truncation_limit": 2,
            "show_asset_id": 1,
            "detection_updated_since": "2024-04-01T00:00:00Z",
            "status": "New,Active,Re-Opened,Fixed",
            "include_vuln_type": "confirmed",
            "arf_kernel_filter": "0",
            "arf_service_filter": "0",
            "arf_config_filter": "0",
            "show_tags": "1",
        }
        api = ConnectorFactory.get_api(technology_id="qualys_vmpc", version_id="v2_0")
        result = api.list_detections(**params)

        self.assertEqual(
            result,
            (
                [
                    {
                        "ID": "6506432",
                        "IP": "***********",
                        "TRACKING_METHOD": "IP",
                        "OS": "Windows 2008 R2 Enterprise Service Pack 1",
                        "DNS": "2k8r2-u-10-11",
                        "NETBIOS": "2K8R2-U-10-11",
                        "LAST_SCAN_DATETIME": "2018-04-13T03:49:05Z",
                        "LAST_VM_SCANNED_DATE": "2018-04-13T03:48:50Z",
                        "LAST_VM_SCANNED_DURATION": "352",
                        "DETECTION_LIST": {
                            "DETECTION": [
                                {
                                    "UNIQUE_VULN_ID": "200",
                                    "QID": "38170",
                                    "TYPE": "Confirmed",
                                    "SEVERITY": "2",
                                    "PORT": "3389",
                                    "PROTOCOL": "tcp",
                                    "SSL": "1",
                                    "RESULTS": "Certificate #0 CN=2k8r2-u-10-11 (2k8r2-u-10-11) doesn&apos;t resolve",
                                    "STATUS": "Active",
                                    "FIRST_FOUND_DATETIME": "2018-01-26T04:45:50Z",
                                    "LAST_FOUND_DATETIME": "2018-04-13T03:48:50Z",
                                    "TIMES_FOUND": "111",
                                    "LAST_TEST_DATETIME": "2018-04-13T03:48:50Z",
                                    "LAST_REOPENED_DATETIME": "2018-04-13T03:46:50Z",
                                    "LAST_UPDATE_DATETIME": "2018-04-13T03:48:50Z",
                                    "IS_IGNORED": "0",
                                    "IS_DISABLED": "0",
                                    "LAST_PROCESSED_DATETIME": "2018-04-13T03:49:05Z",
                                    "AFFECT_RUNNING_KERNEL": "1",
                                    "AFFECT_RUNNING_SERVICE": "0",
                                },
                                {
                                    "UNIQUE_VULN_ID": "300",
                                    "QID": "38173",
                                    "TYPE": "Confirmed",
                                    "SEVERITY": "2",
                                    "PORT": "3389",
                                    "PROTOCOL": "tcp",
                                    "SSL": "1",
                                    "RESULTS": "Certificate #0 CN=2k8r2-u-10-11 unable to get local issuer certificate",
                                    "STATUS": "Active",
                                    "FIRST_FOUND_DATETIME": "2018-01-26T04:45:50Z",
                                    "LAST_FOUND_DATETIME": "2018-04-13T03:48:50Z",
                                    "TIMES_FOUND": "111",
                                    "LAST_TEST_DATETIME": "2018-04-13T03:48:50Z",
                                    "LAST_UPDATE_DATETIME": "2018-04-13T03:49:05Z",
                                    "IS_IGNORED": "0",
                                    "IS_DISABLED": "0",
                                    "LAST_PROCESSED_DATETIME": "2018-04-13T03:49:05Z",
                                    "AFFECT_RUNNING_KERNEL": "0",
                                    "AFFECT_RUNNING_SERVICE": "1",
                                    "AFFECT_EXPLOITABLE_CONFIG": "0",
                                },
                            ]
                        },
                    },
                    {
                        "ID": "4506433",
                        "IP": "***********",
                        "TRACKING_METHOD": "IP",
                        "OS": "Windows 2008 R2 Enterprise Service Pack 2",
                        "DNS": "2k8r2-u-10-12",
                        "NETBIOS": "2K8R2-U-10-12",
                        "LAST_SCAN_DATETIME": "2018-04-13T03:49:05Z",
                        "LAST_VM_SCANNED_DATE": "2018-04-13T03:48:50Z",
                        "LAST_VM_SCANNED_DURATION": "352",
                        "DETECTION_LIST": {
                            "DETECTION": [
                                {
                                    "UNIQUE_VULN_ID": "400",
                                    "QID": "48170",
                                    "TYPE": "Confirmed",
                                    "SEVERITY": "2",
                                    "PORT": "3389",
                                    "PROTOCOL": "tcp",
                                    "SSL": "1",
                                    "RESULTS": "Certificate #0 CN=2k8r2-u-10-11 (2k8r2-u-10-11) doesn&apos;t resolve",
                                    "STATUS": "Active",
                                    "FIRST_FOUND_DATETIME": "2018-01-26T04:45:50Z",
                                    "LAST_FOUND_DATETIME": "2018-04-13T03:48:50Z",
                                    "TIMES_FOUND": "111",
                                    "LAST_TEST_DATETIME": "2018-04-13T03:48:50Z",
                                    "LAST_UPDATE_DATETIME": "2018-04-13T03:49:05Z",
                                    "IS_IGNORED": "0",
                                    "IS_DISABLED": "0",
                                    "LAST_PROCESSED_DATETIME": "2018-04-13T03:49:05Z",
                                    "AFFECT_EXPLOITABLE_CONFIG": "1",
                                },
                            ]
                        },
                    },
                ],
                "9824981",
            ),
        )

    @responses.activate
    def test_list_vendor_vulnerabilities(self):
        setup_list_vendor_vulnerabilities()

        params = {
            "last_modified_after": "2024-04-01T00:00:00Z",
            "details": "All",
        }
        api = ConnectorFactory.get_api(technology_id="qualys_vmpc", version_id="v2_0")
        result = list(api.list_knowledge_base_vuln_stream(**params))

        self.assertEqual(
            result[0],
            {
                "QID": "11699",
                "VULN_TYPE": "Vulnerability",
                "SEVERITY_LEVEL": "5",
                "TITLE": "VMware vCenter Server Remote Code Execution Vulnerability (VMSA-2021-0002)",
                "CATEGORY": "CGI",
                "LAST_SERVICE_MODIFICATION_DATETIME": "2024-04-03T00:00:01Z",
                "PUBLISHED_DATETIME": "2021-02-25T14:31:08Z",
                "PATCHABLE": "1",
                "SOLUTION": """Refer to Chrome security advisory <A HREF="https://chromereleases.googleblog.com/2024/08/stable-channel-update-for-desktop.html" TARGET="_blank">127.0.6533.99</A> for updates and patch information.<P>Patch:<BR>Following are links for downloading patches to fix the vulnerabilities:<P> <A HREF="https://chromereleases.googleblog.com/2024/08/stable-channel-update-for-desktop.html" TARGET="_blank">Google Chrome 127.0.6533.99</A>""",
                "SOFTWARE_LIST": {
                    "SOFTWARE": [{"PRODUCT": "vcenter", "VENDOR": "vmware"}]
                },
                "VENDOR_REFERENCE_LIST": {
                    "VENDOR_REFERENCE": [
                        {
                            "ID": "VMSA-2021-0002",
                            "URL": "https://www.vmware.com/security/advisories/VMSA-2021-0002.html",
                        }
                    ]
                },
                "CVE_LIST": {
                    "CVE": [
                        {
                            "ID": "CVE-2021-21972",
                            "URL": "http://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-21972",
                        },
                        {
                            "ID": "CVE-2021-21973",
                            "URL": "http://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-21973",
                        },
                    ]
                },
                "CVSS": {
                    "ACCESS": {"COMPLEXITY": "2", "VECTOR": "2"},
                    "AUTHENTICATION": "3",
                    "BASE": {"#text": "5.4", "@source": "service"},
                    "EXPLOITABILITY": "1",
                    "IMPACT": {
                        "AVAILABILITY": "2",
                        "CONFIDENTIALITY": "1",
                        "INTEGRITY": "3",
                    },
                    "REMEDIATION_LEVEL": "1",
                    "REPORT_CONFIDENCE": "3",
                    "TEMPORAL": "4.0",
                    "VECTOR_STRING": "CVSS:2.0/AV:A/AC:M/Au:M/C:N/I:C/A:P/E:U/RL:OF/RC:C",
                },
                "CVSS_V3": {
                    "ATTACK": {"COMPLEXITY": "1", "VECTOR": "1"},
                    "BASE": "8.6",
                    "CVSS3_VERSION": "3.1",
                    "EXPLOIT_CODE_MATURITY": "1",
                    "IMPACT": {
                        "AVAILABILITY": "1",
                        "CONFIDENTIALITY": "3",
                        "INTEGRITY": "1",
                    },
                    "PRIVILEGES_REQUIRED": "1",
                    "REMEDIATION_LEVEL": "1",
                    "REPORT_CONFIDENCE": "3",
                    "SCOPE": "2",
                    "TEMPORAL": "7.5",
                    "USER_INTERACTION": "1",
                    "VECTOR_STRING": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:C/C:H/I:N/A:N/E:U/RL:O/RC:C",
                },
                "PCI_FLAG": "1",
                "THREAT_INTELLIGENCE": {
                    "THREAT_INTEL": [
                        {"@id": "2", "#text": "Exploit_Public"},
                        {"@id": "3", "#text": "Active_Attacks"},
                        {"@id": "4", "#text": "High_Lateral_Movement"},
                        {"@id": "5", "#text": "Easy_Exploit"},
                        {"@id": "6", "#text": "High_Data_Loss"},
                        {"@id": "7", "#text": "Denial_of_Service"},
                        {"@id": "9", "#text": "Malware"},
                        {"@id": "11", "#text": "Wormable"},
                        {"@id": "12", "#text": "Predicted_High_Risk"},
                        {"@id": "14", "#text": "Unauthenticated_Exploitation"},
                        {"@id": "15", "#text": "Remote_Code_Execution"},
                        {"@id": "16", "#text": "Ransomware"},
                        {"@id": "18", "#text": "Cisa_Known_Exploited_Vulns"},
                    ]
                },
                "DISCOVERY": {
                    "REMOTE": "1",
                    "ADDITIONAL_INFO": "Patch Available, Exploit Available, Malware Associated",
                },
            },
        )

    @responses.activate
    def test_invalid_url(self):
        api = ConnectorFactory.get_api(
            technology_id="qualys_vmpc", version_id="v2_0", config__url="invalid_url"
        )
        with self.assertRaises(requests.exceptions.ConnectionError):
            api.session_login()
        with self.assertRaises(requests.exceptions.ConnectionError):
            api.list_hosts()
        with self.assertRaises(requests.exceptions.ConnectionError):
            api.list_groups()

    @responses.activate
    def test_session_login(self):
        api = ConnectorFactory.get_api(technology_id="qualys_vmpc", version_id="v2_0")
        setup_session_login(*api.connect.auth)
        self.assertEqual("Logged in", api.session_login())

    @responses.activate
    def test_paginate_no_tags_found(self):
        params = {
            "action": "list",
            "use_tags": "1",
            "tag_set_include": "tag_id_1,tag_id_2",
        }
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/",
            match=[matchers.urlencoded_params_matcher(params)],
            content_type="text/xml",
            body=load_xml_data("data/hosts_no_tags.xml"),
            status=200,
        )

        api = ConnectorFactory.get_api(technology_id="qualys_vmpc", version_id="v2_0")
        pages = list(paginate(api.list_hosts, **params))
        self.assertEqual(len(pages), 1)
        self.assertListEqual(pages[0], [])

    @responses.activate
    def test_paginate_response_empty(self):
        setup_list_groups("data/groups_empty.xml")

        api = ConnectorFactory.get_api(technology_id="qualys_vmpc", version_id="v2_0")
        pages = list(
            paginate(
                api.list_groups,
                ids=["123,456"],
                show_attributes="TITLE",
                truncation_limit=0,
            )
        )
        self.assertEqual(len(pages), 1)
        self.assertListEqual(pages[0], [])

    @responses.activate
    def test_paginate_invalid_parameter(self):
        params = {
            "action": "list",
            "use_tags": "1",
            "tag_set_include": "invalid_tag_1,invalid_tag_2",
        }
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/",
            match=[matchers.urlencoded_params_matcher(params)],
            content_type="text/xml",
            body=load_xml_data("data/invalid_parameter.xml"),
            status=400,
        )

        api = ConnectorFactory.get_api(technology_id="qualys_vmpc", version_id="v2_0")
        pages = list(paginate(api.list_hosts, **params))
        self.assertEqual(len(pages), 1)
        self.assertListEqual(pages[0], [])

    @responses.activate
    def test_paginate_error(self):
        setup_error_response()
        params = {"action": "list"}
        api = ConnectorFactory.get_api(technology_id="qualys_vmpc", version_id="v2_0")
        with self.assertRaises(HTTPError) as e:
            list(paginate(api.list_hosts, **params))
        self.assertEqual(
            "Unexpected error code",
            str(e.exception),
        )

    @responses.activate
    def test_streaming_error(self):
        params = {"action": "list"}
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/knowledge_base/vuln/",
            match=[matchers.urlencoded_params_matcher(params)],
            content_type="text/xml",
            body=load_xml_data("data/error.xml"),
            status=200,
        )

        api = ConnectorFactory.get_api(technology_id="qualys_vmpc", version_id="v2_0")
        with self.assertRaises(HTTPError) as e:
            list(api.list_knowledge_base_vuln_stream(**params))
        self.assertEqual(
            "Unexpected error code",
            str(e.exception),
        )

    @responses.activate
    def test_rate_limit_error_raises_retry(self):
        setup_error_response(error_file="data/error_rate_limited.xml", error_code=409)
        params = {"action": "list"}
        api = ConnectorFactory.get_api(technology_id="qualys_vmpc", version_id="v2_0")
        with self.assertRaises(HTTPError) as ctx:
            list(paginate(api.list_hosts, **params))
            # Qualys API returns 409 for rate limiting
        self.assertEqual(ctx.exception.response.status_code, 409)

    @responses.activate
    def test_concurrency_limit_raises_retry(self):
        setup_error_response(error_file="data/error_concurrency.xml", error_code=409)
        params = {"action": "list"}
        api = ConnectorFactory.get_api(technology_id="qualys_vmpc", version_id="v2_0")
        with self.assertRaises(HTTPError) as ctx:
            list(paginate(api.list_hosts, **params))
            # Qualys API returns 409 for concurrency error
        self.assertEqual(ctx.exception.response.status_code, 409)


class QualysVmpcV20IntegrationTest(BaseIntegrationTest):
    def setUp(self) -> None:
        super().setUp()

    @staticmethod
    def default_settings():
        return {
            "host_sync": {
                "fetch_asset_groups": False,
                "fetch_by_tags": "tag_id_1,tag_id_2",
            }
        }

    @responses.activate
    def test_get_all_hosts(self):
        setup_list_all_hosts()

        self.assert_host_sync_results(
            [
                {
                    "source_id": "9739698",
                    "group_names": [],
                    "hostname": "host1",
                    "fqdns": ["host1.example.com"],
                    "ip_addresses": ["***********"],
                    "mac_addresses": [],
                    "is_internet_facing": True,
                    "internet_exposure": "internet_facing",
                    "os": {
                        "host_type": "workstation",
                        "family": "windows",
                        "name": "Windows 10",
                    },
                    "owners": [],
                    "aad_id": None,
                    "criticality": "tier3",
                    "last_seen": "2024-01-07T21:12:33Z",
                    "source_data": mock.ANY,
                },
                {
                    "source_id": "9824979",
                    "group_names": [],
                    "hostname": "host2",
                    "fqdns": ["host2.example.com"],
                    "ip_addresses": ["***********"],
                    "mac_addresses": [],
                    "is_internet_facing": False,
                    "internet_exposure": "not_internet_facing",
                    "os": {
                        "host_type": "server",
                        "family": "windows",
                        "name": "Windows Server 2019",
                    },
                    "owners": [],
                    "aad_id": None,
                    "criticality": "unknown",
                    "last_seen": "2024-01-09T20:01:26Z",
                    "source_data": mock.ANY,
                },
                {
                    "source_id": "9824981",
                    "group_names": [],
                    "hostname": "host3",
                    "fqdns": ["host3.example.com"],
                    "ip_addresses": ["***********"],
                    "mac_addresses": [],
                    "is_internet_facing": False,
                    "internet_exposure": "not_internet_facing",
                    "os": {
                        "host_type": "workstation",
                        "family": "windows",
                        "name": "Windows 10",
                    },
                    "owners": [],
                    "aad_id": None,
                    "criticality": "unknown",
                    "last_seen": None,
                    "source_data": mock.ANY,
                },
            ],
            IntegrationActionType.VULNERABILITY_ASSET_SYNC,
        )

    @responses.activate
    def test_get_hosts(self):
        setup_list_hosts()

        self.assert_host_sync_results(
            [
                {
                    "source_id": "9739698",
                    "group_names": [],
                    "hostname": "host1",
                    "fqdns": ["host1.example.com"],
                    "ip_addresses": ["***********"],
                    "mac_addresses": [],
                    "is_internet_facing": True,
                    "internet_exposure": "internet_facing",
                    "os": {
                        "host_type": "workstation",
                        "family": "windows",
                        "name": "Windows 10",
                    },
                    "owners": [],
                    "aad_id": None,
                    "criticality": "tier3",
                    "last_seen": "2024-01-07T21:12:33Z",
                    "source_data": mock.ANY,
                },
                {
                    "source_id": "9824979",
                    "group_names": [],
                    "hostname": "host2",
                    "fqdns": ["host2.example.com"],
                    "ip_addresses": ["***********"],
                    "mac_addresses": [],
                    "is_internet_facing": False,
                    "internet_exposure": "not_internet_facing",
                    "os": {
                        "host_type": "server",
                        "family": "windows",
                        "name": "Windows Server 2019",
                    },
                    "owners": [],
                    "aad_id": None,
                    "criticality": "unknown",
                    "last_seen": "2024-01-09T20:01:26Z",
                    "source_data": mock.ANY,
                },
                {
                    "source_id": "9824981",
                    "group_names": [],
                    "hostname": "host3",
                    "fqdns": ["host3.example.com"],
                    "ip_addresses": ["***********"],
                    "mac_addresses": [],
                    "is_internet_facing": False,
                    "internet_exposure": "not_internet_facing",
                    "os": {
                        "host_type": "workstation",
                        "family": "windows",
                        "name": "Windows 10",
                    },
                    "owners": [],
                    "aad_id": None,
                    "criticality": "unknown",
                    "last_seen": None,
                    "source_data": mock.ANY,
                },
            ]
        )

    @responses.activate
    def test_get_hosts_default_settings(self):
        setup_list_hosts_with_groups()
        setup_list_groups()

        self.integration.settings = QualysVmpcV20Settings({})
        self.assert_host_sync_results(
            [
                {
                    "source_id": "9739698",
                    "group_names": ["Asset Group 123", "Asset Group 456"],
                    "hostname": "host1",
                    "fqdns": ["host1.example.com"],
                    "ip_addresses": ["***********"],
                    "mac_addresses": [],
                    "is_internet_facing": False,
                    "internet_exposure": "not_internet_facing",
                    "os": {
                        "host_type": "workstation",
                        "family": "windows",
                        "name": "Windows 10",
                    },
                    "owners": [],
                    "aad_id": None,
                    "criticality": "unknown",
                    "last_seen": "2024-01-07T21:12:33Z",
                    "source_data": mock.ANY,
                },
                {
                    "source_id": "9824979",
                    "group_names": ["Asset Group 123"],
                    "hostname": "host2",
                    "fqdns": ["host2.example.com"],
                    "ip_addresses": ["***********"],
                    "mac_addresses": [],
                    "is_internet_facing": False,
                    "internet_exposure": "not_internet_facing",
                    "os": {
                        "host_type": "server",
                        "family": "windows",
                        "name": "Windows Server 2019",
                    },
                    "owners": [],
                    "aad_id": None,
                    "criticality": "unknown",
                    "last_seen": "2024-01-09T20:01:26Z",
                    "source_data": mock.ANY,
                },
            ]
        )
        # we should only call the groups endpoint once per page
        self.assertTrue(
            responses.assert_call_count(
                "https://qualysapi.qualys.com/api/2.0/fo/asset/group/", 1
            )
        )

    @responses.activate
    def test_get_hosts_normalize_failure(self):
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/",
            content_type="text/xml",
            body=load_xml_data("data/hosts_missing_tags.xml"),
            status=200,
        )

        connector = ConnectorFactory.create(
            technology_id="qualys_vmpc", version_id="v2_0"
        )
        integration = connector.get_integration(decrypt_config=False)

        with self.assertLogs(
            "apps.connectors.integrations.actions.utils", logging.WARNING
        ):
            result = integration.invoke_action(IntegrationActionType.HOST_SYNC)
            result = list(result)

        self.assertEqual(len(result), 1)
        self.assert_activity_logs(
            connector,
            [
                r"Fetch hosts completed. Elapsed time: \d+\.\d+ seconds; Items: 1; Anomalies: 1; Correlation ID: [a-z0-9]+;",
                r"Validation Item 2: 'ID' not found",
                r"Fetch hosts started\. Correlation ID: [a-z0-9]+;",
            ],
        )

    @responses.activate
    def test_get_vendor_vulnerabilities(self):
        setup_list_vendor_vulnerabilities()

        result = self.integration.invoke_action(
            IntegrationActionType.VENDOR_VULNERABILITY_SYNC,
            since="2024-04-01T00:00:00Z",
        )

        vendor_vuln1 = VendorVulnerability(
            source_id="11699",
            technology_id="qualys_vmpc",
            title="VMware vCenter Server Remote Code Execution Vulnerability (VMSA-2021-0002)",
            cves=["CVE-2021-21972", "CVE-2021-21973"],
            patchable=True,
            cvss_v2_base_score=5.4,
            cvss_v3_base_score=8.6,
            solution="""Refer to Chrome security advisory [127.0.6533.99](https://chromereleases.googleblog.com/2024/08/stable-channel-update-for-desktop.html) for updates and patch information.
Patch:
Following are links for downloading patches to fix the vulnerabilities:
 [Google Chrome 127.0.6533.99](https://chromereleases.googleblog.com/2024/08/stable-channel-update-for-desktop.html)""",
        )

        vendor_vuln2 = VendorVulnerability(
            source_id="11771",
            technology_id="qualys_vmpc",
            title="Apache Struts Jakarta Multipart parser Remote Code Execution Vulnerability (S2-045)",
            cves=["CVE-2017-5638"],
            patchable=False,
            solution="",
        )

        result = list(result)
        self.assertEqual(len(result), 2)
        sorted_result = sorted(result, key=lambda x: x.source_id)
        self.assertEqual(sorted_result[0], vendor_vuln1)
        self.assertEqual(sorted_result[1], vendor_vuln2)

    def test_solution_parsing_of_na(self):
        """
        Test parsing the various forms of N/A in the solution field
        results in an empty string.
        """
        na_values = ["N/A", "n/a", "NA", "na", "N/A\n", "N / A "]
        for value in na_values:
            self.assertEqual(html_to_markdown(value), "")

    @responses.activate
    def test_get_detected_vulnerabilities_since(self):
        setup_list_hosts_for_detections()
        setup_list_detected_vulnerabilities()

        integration = ConnectorFactory.get_integration(
            technology_id="qualys_vmpc", version_id="v2_0"
        )

        result = integration.invoke_action(
            IntegrationActionType.DETECTED_VULNERABILITY_SYNC,
            since=datetime(2024, 4, 1, tzinfo=utc),
        )
        asset1 = Host(
            source_id="6506432",
            hostname="2k8r2-u-10-11",
            os=OsAttributes(
                family=OsFamily.WINDOWS,
                name="Windows 2008 R2 Enterprise Service Pack 1",
                host_type=HostType.WORKSTATION,
            ),
            criticality=AssetCriticality.TIER_3,
            ip_addresses=[
                "***********",
            ],
            is_internet_facing=False,
            internet_exposure=InternetExposure.NOT_INTERNET_FACING,
            last_seen=datetime(2018, 4, 13, 3, 48, 50, tzinfo=utc),
        )
        asset2 = Host(
            source_id="4506433",
            hostname="2k8r2-u-10-12",
            os=OsAttributes(
                family=OsFamily.WINDOWS,
                name="Windows 2008 R2 Enterprise Service Pack 2",
                host_type=HostType.WORKSTATION,
            ),
            criticality=AssetCriticality.UNKNOWN,
            ip_addresses=[
                "***********",
            ],
            is_internet_facing=False,
            internet_exposure=InternetExposure.NOT_INTERNET_FACING,
            last_seen=datetime(2018, 4, 13, 3, 48, 50, tzinfo=utc),
        )
        asset3 = Host(
            source_id="9824981",
            hostname="2k8r2-u-10-13",
            os=OsAttributes(
                family=OsFamily.WINDOWS,
                name="Windows 11",
                host_type=HostType.WORKSTATION,
            ),
            criticality=AssetCriticality.UNKNOWN,
            ip_addresses=[
                "***********",
            ],
            is_internet_facing=False,
            internet_exposure=InternetExposure.NOT_INTERNET_FACING,
            last_seen=datetime(2018, 4, 13, 3, 48, 50, tzinfo=utc),
        )

        # non host asset (but has IP)
        asset4 = Host(
            source_id="9824982",
            hostname="***********",
            criticality=AssetCriticality.UNKNOWN,
            os=OsAttributes(
                family=OsFamily.UNKNOWN,
                name="",
                host_type=HostType.UNKNOWN,
            ),
            ip_addresses=[
                "***********",
            ],
            is_internet_facing=False,
            internet_exposure=InternetExposure.NOT_INTERNET_FACING,
            last_seen=datetime(2018, 4, 13, 3, 48, 50, tzinfo=utc),
        )

        # non host asset (but has IP)
        asset5 = Host(
            source_id="9824983",
            hostname="",
            criticality=AssetCriticality.UNKNOWN,
            os=OsAttributes(
                family=OsFamily.UNKNOWN,
                name="",
                host_type=HostType.UNKNOWN,
            ),
            ip_addresses=[],
            is_internet_facing=False,
            internet_exposure=InternetExposure.NOT_INTERNET_FACING,
            last_seen=datetime(2018, 4, 13, 3, 48, 50, tzinfo=utc),
        )

        detected_vuln1 = DetectedVulnerability(
            source_id="200",
            asset=asset1,
            vendor_vulnerability_source_id="38170",
            technology_id="qualys_vmpc",
            first_seen_at=datetime(2018, 1, 26, 4, 45, 50, tzinfo=utc),
            last_activated_at=datetime(2018, 4, 13, 3, 46, 50, tzinfo=utc),
            last_seen_at=datetime(2018, 4, 13, 3, 48, 50, tzinfo=utc),
            active=True,
            status=DetectedVulnerabilityStatus.OPEN,
            confidence_level=DetectedVulnerabilityConfidenceLevel.CONFIRMED,
            kernel_state=DetectedVulnerabilityKernelState.RUNNING_KERNEL,
            service_state=DetectedVulnerabilityServiceState.NON_RUNNING_SERVICE,
            patch_superseded=True,
        )
        detected_vuln2 = DetectedVulnerability(
            source_id="300",
            asset=asset1,
            vendor_vulnerability_source_id="38173",
            technology_id="qualys_vmpc",
            first_seen_at=datetime(2018, 1, 26, 4, 45, 50, tzinfo=utc),
            last_activated_at=datetime(2018, 1, 26, 4, 45, 50, tzinfo=utc),
            last_seen_at=datetime(2018, 4, 13, 3, 48, 50, tzinfo=utc),
            active=True,
            status=DetectedVulnerabilityStatus.OPEN,
            confidence_level=DetectedVulnerabilityConfidenceLevel.CONFIRMED,
            kernel_state=DetectedVulnerabilityKernelState.NON_RUNNING_KERNEL,
            service_state=DetectedVulnerabilityServiceState.RUNNING_SERVICE,
            configuration_state=DetectedVulnerabilityConfigurationState.NON_EXPLOITABLE_CONFIGURATION,
            patch_superseded=True,
        )

        detected_vuln3 = DetectedVulnerability(
            source_id="400",
            asset=asset2,
            vendor_vulnerability_source_id="48170",
            technology_id="qualys_vmpc",
            first_seen_at=datetime(2018, 1, 26, 4, 45, 50, tzinfo=utc),
            last_activated_at=datetime(2018, 1, 26, 4, 45, 50, tzinfo=utc),
            last_seen_at=datetime(2018, 4, 13, 3, 48, 50, tzinfo=utc),
            active=True,
            status=DetectedVulnerabilityStatus.OPEN,
            confidence_level=DetectedVulnerabilityConfidenceLevel.CONFIRMED,
            configuration_state=DetectedVulnerabilityConfigurationState.EXPLOITABLE_CONFIGURATION,
            patch_superseded=False,
        )

        detected_vuln4 = DetectedVulnerability(
            source_id="500",
            asset=asset3,
            vendor_vulnerability_source_id="48170",
            technology_id="qualys_vmpc",
            first_seen_at=datetime(2018, 1, 26, 4, 45, 50, tzinfo=utc),
            last_activated_at=datetime(2018, 1, 26, 4, 45, 50, tzinfo=utc),
            last_seen_at=datetime(2018, 4, 13, 3, 48, 50, tzinfo=utc),
            active=False,
            status=DetectedVulnerabilityStatus.FIXED,
            confidence_level=DetectedVulnerabilityConfidenceLevel.CONFIRMED,
            patch_superseded=True,
        )

        detected_vuln5 = DetectedVulnerability(
            source_id="600",
            asset=asset4,
            vendor_vulnerability_source_id="28170",
            technology_id="qualys_vmpc",
            first_seen_at=datetime(2018, 1, 26, 4, 45, 50, tzinfo=utc),
            last_activated_at=datetime(2018, 4, 13, 3, 46, 50, tzinfo=utc),
            last_seen_at=datetime(2018, 4, 13, 3, 48, 50, tzinfo=utc),
            active=True,
            status=DetectedVulnerabilityStatus.OPEN,
            confidence_level=DetectedVulnerabilityConfidenceLevel.CONFIRMED,
            patch_superseded=True,
        )

        detected_vuln6 = DetectedVulnerability(
            source_id="700",
            asset=asset5,
            vendor_vulnerability_source_id="28170",
            technology_id="qualys_vmpc",
            first_seen_at=datetime(2018, 1, 26, 4, 45, 50, tzinfo=utc),
            last_activated_at=datetime(2018, 4, 13, 3, 46, 50, tzinfo=utc),
            last_seen_at=datetime(2018, 4, 13, 3, 48, 50, tzinfo=utc),
            active=True,
            status=DetectedVulnerabilityStatus.OPEN,
            confidence_level=DetectedVulnerabilityConfidenceLevel.CONFIRMED,
            patch_superseded=True,
        )

        detected_vuln7 = DetectedVulnerability(
            source_id="999",
            asset=asset1,
            vendor_vulnerability_source_id="38199",
            technology_id="qualys_vmpc",
            first_seen_at=datetime(2018, 1, 26, 4, 45, 50, tzinfo=utc),
            last_activated_at=datetime(2018, 4, 13, 3, 46, 50, tzinfo=utc),
            last_seen_at=datetime(2018, 4, 13, 3, 48, 50, tzinfo=utc),
            active=True,
            status=DetectedVulnerabilityStatus.OPEN,
            confidence_level=DetectedVulnerabilityConfidenceLevel.POTENTIAL,
            patch_superseded=True,
        )

        result = list(result)
        self.assertEqual(len(result), 7)
        sorted_result = sorted(result, key=lambda x: x.source_id)
        self.assertEqual(sorted_result[0], detected_vuln1)
        self.assertEqual(sorted_result[1], detected_vuln2)
        self.assertEqual(sorted_result[2], detected_vuln3)
        self.assertEqual(sorted_result[3], detected_vuln4)

        # non host asset (with IP)
        self.assertEqual(sorted_result[4], detected_vuln5)

        # non host asset (no IP)
        self.assertEqual(sorted_result[5], detected_vuln6)

        # potential vulnerability
        self.assertEqual(sorted_result[6], detected_vuln7)

    @responses.activate
    @patch("django.utils.timezone.now")
    def test_get_detected_vulnerabilities_initial(self, m_now):
        setup_list_hosts_for_detections()

        date_now = datetime(2023, 9, 1, 23, tzinfo=utc)
        m_now.return_value = date_now

        params_active_confirmed = {
            "action": "list",
            "show_asset_id": "1",
            "status": "New,Active,Re-Opened",
            "include_vuln_type": "confirmed",
            "arf_kernel_filter": "0",
            "arf_service_filter": "0",
            "arf_config_filter": "0",
            "show_tags": "1",
        }

        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
            match=[matchers.urlencoded_params_matcher(params_active_confirmed)],
            content_type="text/xml",
            body=load_xml_data(f"data/detections_confirmed_page_1.xml"),
            status=200,
        )
        params_active_confirmed_page_2 = deepcopy(params_active_confirmed)
        params_active_confirmed_page_2["id_min"] = "9824981"
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
            match=[matchers.urlencoded_params_matcher(params_active_confirmed_page_2)],
            content_type="text/xml",
            body=load_xml_data(f"data/detections_confirmed_page_2.xml"),
            status=200,
        )

        params_active_superseded_page_1 = deepcopy(params_active_confirmed)
        params_active_superseded_page_1["filter_superseded_qids"] = "1"
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
            match=[matchers.urlencoded_params_matcher(params_active_superseded_page_1)],
            content_type="text/xml",
            body=load_xml_data(f"data/detections_confirmed_superseded_page_1.xml"),
            status=200,
        )

        params_active_potential = {
            "action": "list",
            "show_asset_id": "1",
            "status": "New,Active,Re-Opened",
            "include_vuln_type": "potential",
            "arf_kernel_filter": "0",
            "arf_service_filter": "0",
            "arf_config_filter": "0",
            "show_tags": "1",
        }

        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
            match=[matchers.urlencoded_params_matcher(params_active_potential)],
            content_type="text/xml",
            body=load_xml_data(f"data/detections_potential_page_1.xml"),
            status=200,
        )

        params_active_potential_superseded_page_1 = deepcopy(params_active_potential)
        params_active_potential_superseded_page_1["filter_superseded_qids"] = "1"
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
            match=[
                matchers.urlencoded_params_matcher(
                    params_active_potential_superseded_page_1
                )
            ],
            content_type="text/xml",
            body=load_xml_data(f"data/detections_confirmed_superseded_page_1.xml"),
            status=200,
        )

        (date_now - timedelta(days=30)).strftime("%Y-%m-%dT%H:%M:%SZ")
        params_confirmed_history = {
            "action": "list",
            "show_asset_id": "1",
            "detection_updated_since": (date_now - timedelta(days=30)).strftime(
                "%Y-%m-%dT%H:%M:%SZ"
            ),
            "include_vuln_type": "confirmed",
            "arf_kernel_filter": "0",
            "arf_service_filter": "0",
            "arf_config_filter": "0",
            "show_tags": "1",
            "status": "Fixed",
        }
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
            match=[matchers.urlencoded_params_matcher(params_confirmed_history)],
            content_type="text/xml",
            body=load_xml_data(f"data/detections_confirmed_page_1.xml"),
            status=200,
        )
        params_confirmed_history_page_2 = deepcopy(params_confirmed_history)
        params_confirmed_history_page_2["id_min"] = "9824981"
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
            match=[matchers.urlencoded_params_matcher(params_confirmed_history_page_2)],
            content_type="text/xml",
            body=load_xml_data(f"data/detections_confirmed_page_2.xml"),
            status=200,
        )
        params_confirmed_history_superseded_page_1 = deepcopy(params_confirmed_history)
        params_confirmed_history_superseded_page_1["filter_superseded_qids"] = "1"
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
            match=[
                matchers.urlencoded_params_matcher(
                    params_confirmed_history_superseded_page_1
                )
            ],
            content_type="text/xml",
            body=load_xml_data(f"data/detections_confirmed_superseded_page_1.xml"),
            status=200,
        )

        params_potential_history = {
            "action": "list",
            "show_asset_id": "1",
            "detection_updated_since": (date_now - timedelta(days=30)).strftime(
                "%Y-%m-%dT%H:%M:%SZ"
            ),
            "include_vuln_type": "potential",
            "arf_kernel_filter": "0",
            "arf_service_filter": "0",
            "arf_config_filter": "0",
            "show_tags": "1",
            "status": "Fixed",
        }
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
            match=[matchers.urlencoded_params_matcher(params_potential_history)],
            content_type="text/xml",
            body=load_xml_data(f"data/detections_potential_page_1.xml"),
            status=200,
        )
        params_potential_history_superseded = deepcopy(params_potential_history)
        params_potential_history_superseded["filter_superseded_qids"] = "1"
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
            match=[
                matchers.urlencoded_params_matcher(params_potential_history_superseded)
            ],
            body=load_xml_data(f"data/detections_potential_page_1.xml"),
            content_type="text/xml",
            status=200,
        )

        integration = ConnectorFactory.get_integration(
            technology_id="qualys_vmpc", version_id="v2_0"
        )

        result = integration.invoke_action(
            IntegrationActionType.DETECTED_VULNERABILITY_SYNC,
        )

        result = list(result)
        self.assertEqual(len(result), 14)

    @responses.activate
    def test_eula_accepted(self):
        setup_eula_accepted()
        response = self.integration.invoke("accept_eula")
        self.assertEqual("SUCCESS", response["@status"])

    @responses.activate
    def test_eula_already_accepted(self):
        setup_eula_accepted(already_accepted=True)
        response = self.integration.invoke("accept_eula")
        self.assertEqual("FAILED", response["@status"])
        self.assertIn("already accepted", response["#text"])


class QualysVmpcV20HealthCheckTestTest(BaseTestCase):
    def setUp(self) -> None:
        super().setUp()
        self.connector = ConnectorFactory(
            technology_id="qualys_vmpc",
            version_id="v2_0",
            config__url="https://qualysapi.qualys.com",
            enabled_actions=[
                "host_sync",
                "detected_vulnerability_sync",
                "vendor_vulnerability_sync",
            ],
        )

        self.params = {
            "action": "list",
            "truncation_limit": "1",
        }

    @responses.activate
    def test_connection(self):
        setup_session_login(
            self.connector.config["username"], self.connector.config["password"]
        )
        health_check = ConnectionHealthCheck(
            integration=self.connector.get_integration(decrypt_config=False)
        )
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_connection_error(self):
        responses.post(
            "https://qualysapi.qualys.com//api/2.0/fo/session/",
            body=requests.exceptions.ConnectionError(),
        )
        health_check = ConnectionHealthCheck(
            integration=self.connector.get_integration(decrypt_config=False)
        )
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)

    @responses.activate
    def test_connection_session_login_failure(self):
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/session/",
            content_type="text/xml",
            body=load_xml_data("data/session_login_error.xml"),
            status=200,
        )
        health_check = ConnectionHealthCheck(
            integration=self.connector.get_integration(decrypt_config=False)
        )
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)

    @responses.activate
    def test_read_hosts(self):
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/",
            match=[matchers.urlencoded_params_matcher(self.params)],
            content_type="text/xml",
            body=load_xml_data("data/hosts_page_1.xml"),
            status=200,
        )
        health_check = ReadHosts(
            integration=self.connector.get_integration(decrypt_config=False)
        )
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_read_hosts_error(self):
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/",
            body="",
            status=400,
        )
        health_check = ReadHosts(
            integration=self.connector.get_integration(decrypt_config=False)
        )
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)

    @responses.activate
    def test_read_detections(self):
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
            match=[matchers.urlencoded_params_matcher(self.params)],
            content_type="text/xml",
            body=load_xml_data("data/detections_confirmed_page_1.xml"),
            status=200,
        )
        health_check = ReadDetections(
            integration=self.connector.get_integration(decrypt_config=False)
        )
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_read_detections_error(self):
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
            body="",
            status=400,
        )
        health_check = ReadDetections(
            integration=self.connector.get_integration(decrypt_config=False)
        )
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)

    @responses.activate
    def test_read_knowledge_base(self):
        knowledge_base_params = {"ids": "1", "action": "list"}
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/knowledge_base/vuln/",
            match=[matchers.urlencoded_params_matcher(knowledge_base_params)],
            content_type="text/xml",
            body=load_xml_data("data/knowledge_base.xml"),
            status=200,
        )
        health_check = ReadKnowledgeBase(
            integration=self.connector.get_integration(decrypt_config=False)
        )
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_read_knowledge_base_error(self):
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/knowledge_base/vuln/",
            body="",
            status=400,
        )
        health_check = ReadKnowledgeBase(
            integration=self.connector.get_integration(decrypt_config=False)
        )
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)

    @responses.activate
    def test_eula_accepted_passed(self):
        setup_eula_accepted()
        health_check = EulaAccepted(
            integration=self.connector.get_integration(decrypt_config=False)
        )
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_eula_accepted_fail(self):
        setup_eula_accepted(fail=True)
        health_check = EulaAccepted(
            integration=self.connector.get_integration(decrypt_config=False)
        )
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)


class QualysVmpcV20HealthCheckComponentsTest(
    BaseTestCase, HealthCheckComponentTestMixin
):
    def setUp(self) -> None:
        super().setUp()
        self._patch_encryption()
        self.connector = ConnectorFactory(
            technology_id="qualys_vmpc",
            version_id="v2_0",
            config__url="https://qualysapi.qualys.com",
            enabled_actions=[
                "host_sync",
                "detected_vulnerability_sync",
                "vendor_vulnerability_sync",
            ],
        )

    @parameterized.expand([(False,), (True,)])
    @responses.activate
    def test_components(self, eula_already_accepted):
        params = {
            "action": "list",
            "truncation_limit": "1",
        }
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/",
            match=[matchers.urlencoded_params_matcher(params)],
            content_type="text/xml",
            body=load_xml_data("data/hosts_page_1.xml"),
            status=200,
        )
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
            match=[matchers.urlencoded_params_matcher(params)],
            content_type="text/xml",
            body=load_xml_data("data/detections_confirmed_page_1.xml"),
            status=200,
        )
        knowledge_base_params = {"action": "list", "ids": "1"}
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/knowledge_base/vuln/",
            match=[matchers.urlencoded_params_matcher(knowledge_base_params)],
            content_type="text/xml",
            body=load_xml_data("data/knowledge_base.xml"),
            status=200,
        )
        setup_eula_accepted(already_accepted=eula_already_accepted)
        setup_session_login(
            self.connector.config["username"], self.connector.config["password"]
        )

        components = HealthCheckComponent.get_components(connector=self.connector)

        host_sync_expected = [
            HealthCheckRequirement(
                name="Read hosts",
                description="Read hosts from Qualys Cloud Platform",
                value="assets:read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
        ]
        detected_vuln_sync = [
            HealthCheckRequirement(
                name="Read detections",
                description="Read detections from Qualys Cloud Platform",
                value="detected_vulnerabilities:read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
            HealthCheckRequirement(
                name="Read hosts",
                description="Read hosts from Qualys Cloud Platform",
                value="assets:read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
        ]
        vendor_vuln_sync = [
            HealthCheckRequirement(
                name="Read knowledge base",
                description="Read vulnerabilities from Qualys’ KnowledgeBase",
                value="vendor_vulnerabilities:read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
        ]

        eula_expected = [
            HealthCheckRequirement(
                name="EULA accepted",
                description="EULA accepted by API User in Qualys Cloud Platform",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
        ]

        critical_expected = [
            HealthCheckRequirement(
                name="Connection is valid",
                description="Can connect to the integration API",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
        ]

        self.assert_components(
            components,
            [
                critical_expected,
                eula_expected,
                host_sync_expected,
                detected_vuln_sync,
                vendor_vuln_sync,
            ],
        )

    @responses.activate
    def test_components_failed(self):
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/",
            body="",
            status=400,
        )
        responses.post(
            "https://qualysapi.qualys.com/api/2.0/fo/asset/host/vm/detection/",
            body="",
            status=400,
        )

        setup_eula_accepted()

        components = HealthCheckComponent.get_components(connector=self.connector)

        host_sync_expected = [
            HealthCheckRequirement(
                name="Read hosts",
                description="Read hosts from Qualys Cloud Platform",
                value="assets:read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            ),
        ]
        detected_vuln_sync = [
            HealthCheckRequirement(
                name="Read detections",
                description="Read detections from Qualys Cloud Platform",
                value="detected_vulnerabilities:read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            ),
            HealthCheckRequirement(
                name="Read hosts",
                description="Read hosts from Qualys Cloud Platform",
                value="assets:read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            ),
        ]
        vendor_vuln_sync = [
            HealthCheckRequirement(
                name="Read knowledge base",
                description="Read vulnerabilities from Qualys’ KnowledgeBase",
                value="vendor_vulnerabilities:read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            ),
        ]

        eula_expected = [
            HealthCheckRequirement(
                name="EULA accepted",
                description="EULA accepted by API User in Qualys Cloud Platform",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.PASSED,
            ),
        ]

        critical_expected = [
            HealthCheckRequirement(
                name="Connection is valid",
                description="Can connect to the integration API",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            ),
        ]

        self.assert_components(
            components,
            [
                critical_expected,
                eula_expected,
                host_sync_expected,
                detected_vuln_sync,
                vendor_vuln_sync,
            ],
        )

    @responses.activate
    def test_components_invalid_url(self):
        components = HealthCheckComponent.get_components(connector=self.connector)

        host_sync_expected = [
            HealthCheckRequirement(
                name="Read hosts",
                description="Read hosts from Qualys Cloud Platform",
                value="assets:read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            ),
        ]
        detected_vuln_sync = [
            HealthCheckRequirement(
                name="Read detections",
                description="Read detections from Qualys Cloud Platform",
                value="detected_vulnerabilities:read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            ),
            HealthCheckRequirement(
                name="Read hosts",
                description="Read hosts from Qualys Cloud Platform",
                value="assets:read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            ),
        ]
        vendor_vuln_sync = [
            HealthCheckRequirement(
                name="Read knowledge base",
                description="Read vulnerabilities from Qualys’ KnowledgeBase",
                value="vendor_vulnerabilities:read",
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            ),
        ]

        eula_expected = [
            HealthCheckRequirement(
                name="EULA accepted",
                description="EULA accepted by API User in Qualys Cloud Platform",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            ),
        ]

        critical_expected = [
            HealthCheckRequirement(
                name="Connection is valid",
                description="Can connect to the integration API",
                value=None,
                required=RequirementStatus.REQUIRED,
                status=ValidationStatus.FAILED,
            ),
        ]

        self.assert_components(
            components,
            [
                critical_expected,
                eula_expected,
                host_sync_expected,
                detected_vuln_sync,
                vendor_vuln_sync,
            ],
        )

    @responses.activate
    def test_permissions_no_actions(self):
        self.connector.enabled_actions = []
        components = HealthCheckComponent.get_components(
            connector=self.connector, component_type=ComponentType.PERMISSION
        )
        self.assertEqual(len(components), 0)

    @responses.activate
    def test_permissions_actions(self):
        self.connector.enabled_actions = [
            "vendor_vulnerability_sync",
        ]
        components = HealthCheckComponent.get_components(
            connector=self.connector, component_type=ComponentType.PERMISSION
        )
        self.assertEqual(len(components), 1)
        self.assertEqual(
            components[0].requirements,
            [
                HealthCheckRequirement(
                    name="Read knowledge base",
                    description="Read vulnerabilities from Qualys’ KnowledgeBase",
                    value="vendor_vulnerabilities:read",
                    required=RequirementStatus.REQUIRED,
                    status=ValidationStatus.FAILED,
                )
            ],
        )
