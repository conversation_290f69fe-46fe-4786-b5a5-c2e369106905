import io
import os
from unittest import TestCase

import requests
from requests import HTTPError

from apps.connectors.integrations.error_handling import RecoverableError
from apps.connectors.integrations.vendors.qualys.qualys_vmpc.v2_0.retry import (
    RetryOnQualysRateLimit,
)


class QualysRetryOnTests(TestCase):
    def build_error(self, error_file: str, error_code: int):
        dir_path = os.path.dirname(os.path.realpath(__file__))
        file_path = os.path.join(dir_path, error_file)
        with open(file_path, "r") as f:
            content = f.read()

        # Note: responses.Response() is not the same as requests.Response(),
        # there are internal differences that can cause issues.
        #
        # For example, requests.Response() has a raw attribute while responses has a
        # content attribute
        response = requests.Response()
        response.status_code = error_code
        response.raw = io.BytesIO(content.encode("utf-8"))
        response.encoding = "xml"
        response.url = "https://qualys.example.com/api/2.0/fo/asset/host/"

        error = HTTPError()
        error.response = response
        return error

    def test_parse_concurrency_error(self):
        error = self.build_error(
            error_file="data/error_concurrency.xml", error_code=409
        )
        retry = RetryOnQualysRateLimit()
        with self.assertRaises(RecoverableError) as ctx:
            retry.raise_for_retry(error)
            self.assertEqual(ctx.exception.retry_settings.max_retries, 3)
            self.assertEqual(
                ctx.exception.retry_settings.retry_after.total_seconds(), 60 * 5
            )

    def test_parse_rate_limit_error(self):
        error = self.build_error(
            error_file="data/error_rate_limited.xml", error_code=409
        )
        retry = RetryOnQualysRateLimit()
        with self.assertRaises(RecoverableError) as ctx:
            retry.raise_for_retry(error)
            # The seconds to wait is parsed from the XML response
            self.assertEqual(
                ctx.exception.retry_settings.retry_after.total_seconds(), 68928
            )
            # The max retries is set to 3 by default
            self.assertEqual(ctx.exception.retry_settings.max_retries, 3)

    def test_parse_other_http_error(self):
        """`raise_for_retry` is internal to the library. This test simply documents
        that `raise_for_retry` does not
        perform any
        validation - it will raise RecoverableError for any HTTPError"""
        error = self.build_error(error_file="data/error.xml", error_code=400)
        retry = RetryOnQualysRateLimit()
        with self.assertRaises(RecoverableError) as ctx:
            retry.raise_for_retry(error)
            self.assertEqual(ctx.exception.retry_settings.max_retries, 3)
            self.assertEqual(
                ctx.exception.retry_settings.retry_after.total_seconds(), 60 * 5
            )
