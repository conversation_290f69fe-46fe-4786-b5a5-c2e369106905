import json

from apps.connectors.integrations.actions.external_ticket_actions import (
    CreateExternalTicketArgs,
)
from apps.connectors.integrations.actions.update_lifecycle_status import (
    CorrIncidentStatus,
)
from apps.connectors.integrations.vendors.atlassian.jira.utils import (
    extract_linked_attachments_from_adf,
    get_issue_description,
    get_issue_fields,
)
from apps.connectors.integrations.vendors.atlassian.jira.v1.settings import (
    JiraCreateIssueSettings,
)
from apps.tests.base import BaseTestCase


def load_json_data(file_name):
    path = f"apps/connectors/tests/integrations/data/jira"
    with open(f"{path}/{file_name}", "r") as file:
        json_string = file.read()
        return json.loads(json_string)


class JiraV1IntegrationHelpersTest(BaseTestCase):
    def test_get_issue_fields(self):
        settings = JiraCreateIssueSettings(
            project_key="JSMTEST",
            issue_type="10004",
            alert_type="customfield_10124",
            soc_priority="priority.name",
            alert_category="customfield_10093",
        )
        alert_fields = CreateExternalTicketArgs(
            alert_id="alert123",
            alert_title="Test New Issue - Response",
            alert_organization_name="JSMTEST",
            created_at="2023-10-01T12:00:00Z",
            assigned_user_display="user1",
            corr_status=CorrIncidentStatus("new"),
            alert_category="Malware",
            alert_type="Task",
            alert_link="https://example.com/alert123",
            assigned_group="Security Team",
            integration_name="MDR",
            soc_priority="Low",
            vendor_severity="Low",
        )
        issue_fields = get_issue_fields(
            alert_fields=alert_fields.model_dump(),
            field_settings=settings.model_dump(),
            project_key=settings.project_key,
            issue_type=settings.issue_type,
        )
        expected_fields = {
            "summary": "Test New Issue - Response",
            "project": {"key": "JSMTEST"},
            "issuetype": {"id": "10004"},
            "description": {
                "type": "doc",
                "version": 1,
                "content": [
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": "Alert id: ",
                                "marks": [{"type": "strong"}],
                            },
                            {"type": "text", "text": "alert123"},
                        ],
                    },
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": "Alert title: ",
                                "marks": [{"type": "strong"}],
                            },
                            {"type": "text", "text": "Test New Issue - Response"},
                        ],
                    },
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": "Alert organization name: ",
                                "marks": [{"type": "strong"}],
                            },
                            {"type": "text", "text": "JSMTEST"},
                        ],
                    },
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": "Alert category: ",
                                "marks": [{"type": "strong"}],
                            },
                            {"type": "text", "text": "Malware"},
                        ],
                    },
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": "Alert type: ",
                                "marks": [{"type": "strong"}],
                            },
                            {"type": "text", "text": "Task"},
                        ],
                    },
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": "Alert link: ",
                                "marks": [{"type": "strong"}],
                            },
                            {"type": "text", "text": "https://example.com/alert123"},
                        ],
                    },
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": "Assigned group: ",
                                "marks": [{"type": "strong"}],
                            },
                            {"type": "text", "text": "Security Team"},
                        ],
                    },
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": "Integration name: ",
                                "marks": [{"type": "strong"}],
                            },
                            {"type": "text", "text": "MDR"},
                        ],
                    },
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": "Soc priority: ",
                                "marks": [{"type": "strong"}],
                            },
                            {"type": "text", "text": "Low"},
                        ],
                    },
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": "Vendor severity: ",
                                "marks": [{"type": "strong"}],
                            },
                            {"type": "text", "text": "Low"},
                        ],
                    },
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": "Created at: ",
                                "marks": [{"type": "strong"}],
                            },
                            {"type": "text", "text": "2023-10-01 12:00:00+00:00"},
                        ],
                    },
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": "Assigned user display: ",
                                "marks": [{"type": "strong"}],
                            },
                            {"type": "text", "text": "user1"},
                        ],
                    },
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": "Corr status: ",
                                "marks": [{"type": "strong"}],
                            },
                            {"type": "text", "text": "new"},
                        ],
                    },
                ],
            },
            "customfield_10093": "Malware",
            "customfield_10124": "Task",
            "priority": {"name": "Low"},
        }

        self.assertEqual(issue_fields, expected_fields)

    def test_get_issue_description(self):
        event_fields = {
            "assigned_to": "user1",
            "status": "open",
            "datetime_created": "2023-10-01T12:00:00Z",
            "vendor_severity": "low",
        }
        description = get_issue_description(event_fields)

        self.assertEqual(description["type"], "doc")
        self.assertEqual(description["version"], 1)
        self.assertEqual(len(description["content"]), 4)
        self.assertEqual(
            description["content"][0]["content"][0]["text"], "Assigned to: "
        )
        self.assertEqual(description["content"][0]["content"][1]["text"], "user1")

    def test_get_issue_description_with_complex_event_fields(self):
        event_fields = {
            "nested_dict": {"key": "value"},
            "list_field": ["item1", "item2"],
        }
        description = get_issue_description(event_fields)

        self.assertEqual(description["type"], "doc")
        self.assertEqual(description["version"], 1)
        self.assertEqual(len(description["content"]), 2)
        self.assertEqual(
            description["content"][0]["content"][1]["text"], "{'key': 'value'}"
        )
        self.assertEqual(
            description["content"][1]["content"][1]["text"], "item1, item2"
        )

    def test_extract_linked_attachments_from_adf(self):
        comment_body = {
            "type": "doc",
            "version": 1,
            "content": [
                {
                    "type": "paragraph",
                    "content": [
                        {"type": "text", "text": "This is a comment with attachment: "}
                    ],
                },
                {
                    "type": "mediaSingle",
                    "attrs": {"layout": "align-start"},
                    "content": [
                        {
                            "type": "media",
                            "attrs": {
                                "type": "file",
                                "id": "73272855-0f37-49a8-95cd-367d76ca9dc5",
                                "alt": "Screenshot 2025-07-13 at 12.37.46 PM.png",
                                "collection": "",
                                "height": 128,
                                "width": 411,
                            },
                        }
                    ],
                },
            ],
        }
        result = extract_linked_attachments_from_adf(comment_body)
        self.assertEqual(result, ["Screenshot 2025-07-13 at 12.37.46 PM.png"])

        # Test root is a dict, media without alt (should skip)
        comment_body = {"type": "media", "attrs": {}}
        result = extract_linked_attachments_from_adf(comment_body)
        self.assertEqual(result, [])

        # Test with nested content dict
        comment_body = {
            "type": "doc",
            "content": [{"type": "media", "attrs": {"alt": "nested_file.txt"}}],
        }
        result = extract_linked_attachments_from_adf(comment_body)
        self.assertEqual(result, ["nested_file.txt"])

        # Test root is a list with valid media node
        comment_body = [{"type": "media", "attrs": {"alt": "from_list_root.jpg"}}]
        result = extract_linked_attachments_from_adf(comment_body)
        self.assertEqual(result, ["from_list_root.jpg"])

        # Test list with mixed content
        comment_body = [
            {"type": "media", "attrs": {"alt": "valid_file_1.png"}},
            {"type": "media", "attrs": {}},
            {
                "type": "paragraph",
                "content": [{"type": "media", "attrs": {"alt": "deeply_nested.png"}}],
            },
        ]
        result = extract_linked_attachments_from_adf(comment_body)
        self.assertEqual(result, ["valid_file_1.png", "deeply_nested.png"])

        # Test list if dicts with no media nodes
        comment_body = [
            {
                "type": "paragraph",
                "content": [{"type": "text", "text": "No attachments here."}],
            }
        ]
        result = extract_linked_attachments_from_adf(comment_body)
        self.assertEqual(result, [])

        # Test empty input
        result = extract_linked_attachments_from_adf([])
        self.assertEqual(result, [])
