from typing import Generator

import pytest
import requests
from django.test import TestCase
from pydantic import BaseModel

from apps.accounts import Entitlement
from apps.connectors.integrations import (
    ConnectionTemplate,
    Integration,
    Template,
    TemplateVersion,
    TemplateVersionSettings,
)
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.error_handling import RecoverableError, RetryOn
from apps.connectors.integrations.schemas import IntegrationActionArgs
from factories import ConnectorFactory


class FakeActionArgs(IntegrationActionArgs):
    foo: str = "default_value"


class FakeAction(IntegrationAction):
    name = "Recoverable Action"
    action_type = IntegrationActionType.HOST_SYNC
    entitlement = Entitlement.mdr

    def get_permission_checks(self):
        return []


class MockRecoverableAction(FakeAction):
    name = "Recoverable Action"
    recoverable_errors = [
        RetryOn(typ=requests.exceptions.HTTPError, status_code=[409]),
    ]

    def execute(self, args):
        response = requests.Response()
        response.status_code = 409
        exception = requests.HTTPError("Concurrency lock reached", response=response)
        raise exception


class MockNonRecoverableAction(FakeAction):
    name = "Non-Recoverable Action"

    def execute(self, args):
        raise ValueError("This is a non-recoverable error")


class IntegrationsTest(TestCase):
    def test_template_validation(self):
        with self.assertRaises(NotImplementedError):

            class TestTemplate(Template):
                pass

        with self.assertRaises(NotImplementedError):

            class TestTemplateVersion(TemplateVersion):
                pass

        with self.assertRaises(NotImplementedError):

            class TestConnectionTemplate(ConnectionTemplate):
                pass

    def test_integration_actions_must_be_iterable(self):
        # This is OK
        class TestIntegration(Integration):
            actions = (object(),)

        # This is not OK
        with self.assertRaises(ValueError):

            class TestIntegration(Integration):
                actions = object()

    def test_wrap_in_recoverable_error(self):
        # Create a mock action that raises a RecoverableError

        class ExampleIntegration(Integration):
            actions = (MockRecoverableAction,)
            known_exception_types = (requests.HTTPError,)

        settings = TemplateVersionSettings({IntegrationActionType.HOST_SYNC: {}})
        integration = ExampleIntegration(config={}, settings=settings)

        with self.assertRaises(RecoverableError):
            integration.invoke_action(IntegrationActionType.HOST_SYNC, FakeActionArgs())

    def test_reraises_nonrecoverable_error(self):
        # Create a mock action that raises a non-recoverable error

        class ExampleIntegration(Integration):
            actions = (MockNonRecoverableAction,)

        settings = TemplateVersionSettings({IntegrationActionType.HOST_SYNC: {}})
        integration = ExampleIntegration(config={}, settings=settings)
        with self.assertRaises(ValueError):
            integration.invoke_action(IntegrationActionType.HOST_SYNC, FakeActionArgs())

    def test_validate_action_no_metadata(self):
        class BaseDummyIntegrationAction(IntegrationAction):
            action_type = "test/dummy_action"

        class DummyIntegrationAction(BaseDummyIntegrationAction):
            metadata = None

    def test_validate_action_undefined_global_action(self):
        with pytest.raises(ValueError):

            class DummyIntegrationAction(IntegrationAction):
                action_type = "dummy_action"

    def test_validate_action_no_type(self):
        with pytest.raises(ValueError):

            class DummyIntegrationAction(IntegrationAction):
                metadata = None

    def test_validate_action_metadata(self):
        class DummyArgs(IntegrationActionArgs):
            pass

        class DummyResult(BaseModel):
            pass

        class BaseDummyIntegrationAction(IntegrationAction):
            action_type = "test/dummy_action"
            metadata = IntegrationActionMetadata(
                args_type=DummyArgs,
                result_type=DummyResult,
            )

            def execute(self, args: DummyArgs) -> DummyResult:
                pass

        class CorrectTypeHints(BaseDummyIntegrationAction):
            def execute(self, args: DummyArgs) -> DummyResult:
                pass

        class CorrectTypeHintsGenerator(BaseDummyIntegrationAction):
            def execute(self, args: DummyArgs) -> Generator[DummyResult, None, None]:
                pass

        with pytest.raises(ValueError):

            class IncorrectTypeHints(BaseDummyIntegrationAction):
                def execute(self, args: str) -> str:
                    pass

        with pytest.raises(ValueError):

            class IncorrectReturnTypeHint(BaseDummyIntegrationAction):
                def execute(self, args: DummyArgs) -> str:
                    pass

        with pytest.raises(ValueError):

            class IncorrectArgumentTypeHint(BaseDummyIntegrationAction):
                def execute(self, args: str) -> DummyResult:
                    pass

        # Reset IntegrationAction to its original state
        del IntegrationAction._all_types[BaseDummyIntegrationAction.action_type]

    def test_validate_mdr_action_metadata(self):
        class DummyArgs(IntegrationActionArgs):
            pass

        class DummyResult(BaseModel):
            pass

        with self.assertRaises(ValueError) as context:

            class MDRDummyIntegrationAction(IntegrationAction):
                action_type = "dummy/mdr_action"
                entitlement = Entitlement.mdr
                metadata = IntegrationActionMetadata(
                    args_type=DummyArgs,
                    result_type=DummyResult,
                )

                def execute(self, args: DummyArgs) -> DummyResult:
                    pass

        self.assertTrue("must be a subclass of TAPResult" in str(context.exception))

    def test_get_action_health_checks_empty(self):
        integration = ConnectorFactory.get_integration(technology_id="tenable_io")
        health_checks = integration.get_action_health_checks(
            action_type="invalid_action"
        )
        self.assertEqual(health_checks, [])

    def test_action_metadata_raises_error(self):
        class DummyArgs(BaseModel):
            pass

        class DummyResult(BaseModel):
            pass

        class InvalidResult:
            pass

        with self.assertRaises(ValueError):
            IntegrationActionMetadata(
                args_type=DummyArgs,
                result_type=InvalidResult,
            )

        with self.assertRaises(ValueError):
            IntegrationActionMetadata(
                args_type=DummyArgs,
                result_type=DummyResult | InvalidResult,
            )

    def test_poll_action_not_implemented(self):
        integration = ConnectorFactory.get_integration(
            technology_id="tenable_io",
            enabled_actions=["host_sync"],
        )
        with self.assertRaises(NotImplementedError):
            integration.poll_action("host_sync", poll_context=None)
