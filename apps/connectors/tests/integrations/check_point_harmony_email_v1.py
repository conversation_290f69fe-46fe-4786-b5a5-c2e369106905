"""
Tests for Check Point Harmony Email Protection V1 integration.

This test module uses JSON data files stored in the data/check_point_harmony_email directory
rather than hardcoding test data directly in the test file. This approach:

1. Centralizes test data in dedicated files
2. Makes the test code cleaner and more maintainable
3. Allows for easier updates to test data
4. Follows the pattern used in other integration tests

Data files are loaded via a cached function to improve performance.
"""

import json
from functools import cache
from unittest import mock

import responses

import apps.connectors.integrations.vendors.check_point.check_point_harmony_email.v1.actions.event_sync as event_sync
from apps.connectors.integrations import IntegrationActionType
from apps.connectors.integrations.actions.email_threat import (
    EmailThreatRemediationStatus,
)
from apps.connectors.integrations.health_check import IntegrationHealthCheckResult
from apps.connectors.integrations.schemas.identifiers.email_threat_identifier import (
    EmailThreatIdentifier,
    EmailThreatIdentifierArgs,
)
from apps.connectors.integrations.schemas.ocsf import (
    Severity,
)
from apps.connectors.integrations.vendors.check_point.check_point_harmony_email.v1.bookmarks import (
    CheckPointHarmonyEmailV1Bookmarks,
)
from apps.connectors.integrations.vendors.check_point.check_point_harmony_email.v1.health_check import (
    ConnectionHealthCheck,
)
from apps.connectors.tests.integrations.base import (
    BaseIntegrationTest,
    HealthCheckComponentTestMixin,
)
from apps.connectors.utils import serialize
from apps.tests.base import BaseTestCase
from factories import ConnectorFactory


@cache
def load_data(filename):
    """Load test data from JSON files."""
    path = "apps/connectors/tests/integrations/data/check_point_harmony_email"
    with open(f"{path}/{filename}.json", "r") as f:
        return json.load(f)


def mock_auth_response():
    return load_data("auth_response")


def mock_event_query_response():
    return load_data("event_query_response")


def mock_entity_details_response():
    return load_data("entity_details_response")


def setup_auth_mock():
    responses.add(
        responses.POST,
        "https://test_url.com/auth/external",
        json=mock_auth_response(),
        status=200,
    )


def setup_api_responses():
    setup_auth_mock()

    # Event query
    responses.add(
        responses.GET,
        "https://test_url.com/event/query",
        json=mock_event_query_response(),
        status=200,
    )

    # Event by ID
    responses.add(
        responses.GET,
        "https://test_url.com/event/test_event_id",
        json=mock_entity_details_response(),
        status=200,
    )

    responses.add(
        responses.POST,
        "https://test_url.com/v1.0/action/event",
        json={"responseData": {"taskId": "test_task_123"}},
        match=[
            responses.matchers.json_params_matcher(
                {
                    "eventActionName": "quarantine",
                    "eventId": mock.ANY,  # Use ANY to match any event ID
                }
            )
        ],
        status=200,
    )

    responses.add(
        responses.POST,
        "https://test_url.com/v1.0/action/event",
        json={"responseData": {"taskId": "test_task_123"}},
        match=[
            responses.matchers.json_params_matcher(
                {
                    "eventActionName": "restore",
                    "eventId": mock.ANY,  # Use ANY to match any event ID
                }
            )
        ],
        status=200,
    )

    responses.add(
        responses.GET,
        "https://test_url.com/v1.0/task/test_task_123",
        json={"detail": [{"loc": ["string"], "msg": "string", "type": "string"}]},
        status=200,
    )


class CheckPointHarmonyEmailV1ApiTest(BaseTestCase):
    def setUp(self) -> None:
        self.api = ConnectorFactory.get_api(technology_id="check_point_harmony_email")

    @responses.activate
    def test_query_events(self):
        # Mock the auth_token to avoid authentication during testing
        self.api._auth_token = "test_token"
        setup_api_responses()
        result = self.api.query_events(
            start_date="2024-01-15T00:00:00Z", end_date="2024-01-15T23:59:59Z"
        )

        expected = mock_event_query_response()
        self.assertEqual(result, expected)

    @responses.activate
    def test_get_event_by_id(self):
        # Mock the auth_token to avoid authentication during testing
        self.api._auth_token = "test_token"
        setup_api_responses()

        result = self.api.get_event_by_id("test_event_id")

        expected = mock_entity_details_response()
        self.assertEqual(result, expected)

    @responses.activate
    def test_quarantine_event(self):
        self.api._auth_token = "test_token"
        responses.add(
            responses.POST,
            "https://test_url.com/v1.0/action/event",
            json={"responseData": {"taskId": "test_task_123"}},
            status=200,
        )

        result = self.api.quarantine_event("test_event_id")

        expected = {"responseData": {"taskId": "test_task_123"}}
        self.assertEqual(result, expected)

        # Verify the request payload - should only be 1 call since auth_token is already set
        self.assertEqual(len(responses.calls), 1)  # Only quarantine call
        quarantine_call = responses.calls[0]
        request_body = json.loads(quarantine_call.request.body)
        self.assertEqual(request_body["eventId"], "test_event_id")
        self.assertEqual(request_body["eventActionName"], "quarantine")

    @responses.activate
    def test_restore_event(self):
        self.api._auth_token = "test_token"
        responses.add(
            responses.POST,
            "https://test_url.com/v1.0/action/event",
            json={"responseData": {"taskId": "test_task_456"}},
            status=200,
        )

        result = self.api.restore_event("test_event_id")

        expected = {"responseData": {"taskId": "test_task_456"}}
        self.assertEqual(result, expected)

        # Verify the request payload - should only be 1 call since auth_token is already set
        self.assertEqual(len(responses.calls), 1)  # Only restore call
        restore_call = responses.calls[0]
        request_body = json.loads(restore_call.request.body)
        self.assertEqual(request_body["eventId"], "test_event_id")
        self.assertEqual(request_body["eventActionName"], "restore")

    @responses.activate
    def test_get_task_status(self):
        """Test getting the status of an async task."""
        self.api._auth_token = "test_token"
        responses.add(
            responses.GET,
            "https://test_url.com/v1.0/task/test_task_123",
            json={"status": "completed", "result": "success"},
            status=200,
        )

        result = self.api.get_task_status("test_task_123")

        expected = {"status": "completed", "result": "success"}
        self.assertEqual(result, expected)


class CheckPointHarmonyEmailV1IntegrationTest(BaseIntegrationTest):
    def setUp(self) -> None:
        self.integration = ConnectorFactory.get_integration(
            technology_id="check_point_harmony_email",
            version_id="v1",
            enabled_actions=["event_sync"],
        )
        self.event_id = "test_event_id"

    def test_bookmarks(self):
        self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        # The bookmark might be None if the integration is not properly configured for testing
        # Just verify that the bookmark schema exists
        schema = CheckPointHarmonyEmailV1Bookmarks.model_json_schema()
        self.assertIn(IntegrationActionType.EVENT_SYNC.value, schema["properties"])

        schema = schema["properties"][IntegrationActionType.EVENT_SYNC.value]
        self.assertIn("end_date", schema["properties"])

    @responses.activate
    def test_event_sync(self):
        setup_api_responses()
        responses.add(
            responses.GET,
            "https://test_url.com/event/query",
            json=mock_event_query_response(),
            status=200,
        )

        bookmark = self.integration.get_bookmark(IntegrationActionType.EVENT_SYNC)
        bookmark.end_date = "2024-01-15T23:59:59Z"  # Set a valid end time for the query
        response = self.integration.invoke_action(
            IntegrationActionType.EVENT_SYNC,
            **{
                "bookmark": bookmark,
            },
        )

        result = serialize(list(response))

        self.assertEqual(
            result,
            [
                {
                    "raw_event": mock.ANY,
                    "event_timestamp": "2025-07-24T20:58:27.073355Z",
                    "ocsf": {
                        "activity_id": 0,
                        "activity_name": "Unknown",
                        "category_name": "Findings",
                        "category_uid": 2,
                        "class_name": "Detection Finding",
                        "class_uid": 2004,
                        "confidence": "95",
                        "confidence_id": 99,
                        "evidences": [
                            {
                                "email": {
                                    "cc": None,
                                    "files": None,
                                    "from_mailbox": "<EMAIL>",
                                    "is_read": True,
                                    "message_uid": "<<EMAIL>>",
                                    "reply_to": None,
                                    "size": 35009,
                                    "subject": "this is a test email message",
                                    "to": [
                                        "<EMAIL>",
                                        "<EMAIL>",
                                    ],
                                    "urls": [
                                        {
                                            "hostname": "www.checkpoint.com",
                                            "netloc": "www.checkpoint.com",
                                            "scheme": "https",
                                            "url_string": "https://www.checkpoint.com",
                                        }
                                    ],
                                }
                            }
                        ],
                        "finding_info": {
                            "desc": "DLP Engine has detected a leak in ‘please "
                            "see my credit data’ from "
                            "<EMAIL>",
                            "title": "dlp Detection - Low Severity",
                            "types": ["dlp"],
                        },
                        "message": "DLP Engine has detected a leak in ‘please see my credit "
                        "data’ from <EMAIL>",
                        "metadata": {
                            "correlation_uid": "test_event_id",
                            "event_code": None,
                            "product": {
                                "name": "Harmony Email and Collaboration",
                                "vendor_name": "Check Point",
                                "version": "1.0",
                            },
                            "profiles": ["datetime", "host", "security_control"],
                            "uid": "test_event_id",
                            "version": "1.5.0-dev",
                        },
                        "remediation": {
                            "desc": "Available action: dismiss\n"
                            "Available action: severityChange\n"
                            "Available action: severityChange\n"
                            "Available action: severityChange\n"
                            "Available action: severityChange"
                        },
                        "severity": "Low",
                        "severity_id": 2,
                        "status": "Suppressed",
                        "status_id": 3,
                        "time": 1753390707073,
                        "time_dt": "2025-07-24T20:58:27.073355Z",
                        "type_name": "Detection Finding: Unknown",
                        "type_uid": 200400,
                    },
                    "vendor_item_ref": {
                        "id": "@0@test_event_id:",
                        "title": "test_event_id",
                    },
                }
            ],
        )

    @responses.activate
    def test_remediate_threat(self):
        setup_api_responses()

        event_identity = EmailThreatIdentifier(
            value_type="check_point_harmony_email",
            value=self.event_id,
        )
        args = EmailThreatIdentifierArgs(email_threat_id=event_identity)

        remediation_result = self.integration.invoke_action(
            IntegrationActionType.REMEDIATE_EMAIL_THREAT, args
        )

        self.assertEqual(
            remediation_result.result.remediation_status,
            EmailThreatRemediationStatus.IN_PROGRESS,
        )

    @responses.activate
    def test_unremediate_threat(self):
        setup_api_responses()

        event_identity = EmailThreatIdentifier(
            value_type="check_point_harmony_email",
            value=self.event_id,
        )
        args = EmailThreatIdentifierArgs(email_threat_id=event_identity)

        remediation_result = self.integration.invoke_action(
            IntegrationActionType.UNREMEDIATE_EMAIL_THREAT, args
        )

        self.assertEqual(
            remediation_result.result.remediation_status,
            EmailThreatRemediationStatus.IN_PROGRESS,
        )


class CheckPointHarmonyEmailV1HealthCheckComponentsTest(
    BaseTestCase, HealthCheckComponentTestMixin
):
    def setUp(self) -> None:
        self._patch_encryption()

        self.connector = ConnectorFactory(
            technology_id="check_point_harmony_email",
            enabled_actions=["event_sync"],
        )

        self.integration = self.connector.get_integration(decrypt_config=False)

    @responses.activate
    def test_connection(self):
        responses.add(
            responses.POST,
            "https://test_url.com/auth/external",
            json=mock_auth_response(),
            status=200,
        )
        health_check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.PASSED)

    @responses.activate
    def test_connection_failed(self):
        responses.add(
            responses.POST,
            "https://test_url.com/auth/external",
            json={"error": "Authentication failed"},
            status=400,
        )

        health_check = ConnectionHealthCheck(integration=self.integration)
        self.assertEqual(health_check.get_result(), IntegrationHealthCheckResult.FAILED)


class CheckPointHarmonyEmailV1HelpersTest(BaseTestCase):
    def test_map_severity(self):
        self.assertEqual(event_sync.map_severity("critical"), Severity.CRITICAL)
        self.assertEqual(event_sync.map_severity("high"), Severity.HIGH)
        self.assertEqual(event_sync.map_severity("medium"), Severity.MEDIUM)
        self.assertEqual(event_sync.map_severity("low"), Severity.LOW)
        self.assertEqual(event_sync.map_severity("info"), Severity.INFORMATIONAL)
        self.assertEqual(event_sync.map_severity(""), Severity.UNKNOWN)
        self.assertEqual(event_sync.map_severity("unknown_severity"), Severity.UNKNOWN)

    def test_parse_email_links(self):
        # Test single link
        links = "https://example.com/link1"
        parsed = event_sync.parse_email_links(links)
        self.assertEqual(len(parsed), 1)
        self.assertEqual(parsed[0].url_string, "https://example.com/link1")

        # Test multiple links separated by comma
        links = "https://example.com/link1,https://example.com/link2"
        parsed = event_sync.parse_email_links(links)
        self.assertEqual(len(parsed), 2)
        self.assertEqual(parsed[0].url_string, "https://example.com/link1")
        self.assertEqual(parsed[1].url_string, "https://example.com/link2")

    def test_parse_json_field(self):
        # Test valid JSON
        json_str = '{"key": "value"}'
        result = event_sync.parse_json_field(json_str)
        self.assertEqual(result, {"key": "value"})

    def test_extract_emails_from_user_field(self):
        # Test JSON format
        user_json = '{"mail": "<EMAIL>"}'
        emails = event_sync.extract_emails_from_user_field(user_json)
        self.assertEqual(emails, ["<EMAIL>"])

    def test_create_email_evidence_basic(self):
        """Test create_email_evidence with basic entity data."""
        entity_data = {
            "entityPayload": {
                "internetMessageId": "<<EMAIL>>",
                "subject": "Test Subject",
                "fromEmail": "<EMAIL>",
                "to": "<EMAIL>",
                "isRead": "true",
            }
        }

        evidence = event_sync.create_email_evidence(entity_data)

        self.assertIsNotNone(evidence)
        self.assertIsNotNone(evidence.email)
        self.assertEqual(evidence.email.message_uid, "<<EMAIL>>")
        self.assertEqual(evidence.email.subject, "Test Subject")
        self.assertEqual(evidence.email.from_mailbox, "<EMAIL>")
        self.assertEqual(evidence.email.to, ["<EMAIL>"])
        self.assertTrue(evidence.email.is_read)

    def test_create_email_evidence_with_attachments_dict(self):
        """Test create_email_evidence with attachments as dictionary."""
        entity_data = {
            "entityPayload": {
                "internetMessageId": "<<EMAIL>>",
                "subject": "Test with Attachments",
                "attachments": {
                    "file1": {
                        "fileName": "document.pdf",
                        "mimeType": "application/pdf",
                        "size": 1024,
                    },
                    "file2": {
                        "fileName": "image.jpg",
                        "mimeType": "image/jpeg",
                        "size": 2048,
                    },
                },
            }
        }

        evidence = event_sync.create_email_evidence(entity_data)

        self.assertIsNotNone(evidence.email.files)
        self.assertEqual(len(evidence.email.files), 2)

        # Check first file
        file1 = evidence.email.files[0]
        self.assertEqual(file1.name, "document.pdf")
        self.assertEqual(file1.mime_type, "application/pdf")
        self.assertEqual(file1.size, 1024)

        # Check second file
        file2 = evidence.email.files[1]
        self.assertEqual(file2.name, "image.jpg")
        self.assertEqual(file2.mime_type, "image/jpeg")
        self.assertEqual(file2.size, 2048)

    def test_create_email_evidence_with_attachments_list(self):
        """Test create_email_evidence with attachments as list."""
        entity_data = {
            "entityPayload": {
                "internetMessageId": "<<EMAIL>>",
                "subject": "Test with Attachments List",
                "attachments": [
                    {
                        "fileName": "document.pdf",
                        "mimeType": "application/pdf",
                        "size": 1024,
                    },
                    {
                        "fileName": "spreadsheet.xlsx",
                        "mimeType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        "size": 4096,
                    },
                ],
            }
        }

        evidence = event_sync.create_email_evidence(entity_data)

        self.assertIsNotNone(evidence.email.files)
        self.assertEqual(len(evidence.email.files), 2)
        self.assertEqual(evidence.email.files[0].name, "document.pdf")
        self.assertEqual(evidence.email.files[1].name, "spreadsheet.xlsx")

    def test_create_email_evidence_with_email_links(self):
        """Test create_email_evidence with email links."""
        entity_data = {
            "entityPayload": {
                "internetMessageId": "<<EMAIL>>",
                "subject": "Test with Links",
                "emailLinks": "https://example.com/link1,https://example.com/link2",
            }
        }

        evidence = event_sync.create_email_evidence(entity_data)

        self.assertIsNotNone(evidence.email.urls)
        self.assertEqual(len(evidence.email.urls), 2)
        self.assertEqual(evidence.email.urls[0].url_string, "https://example.com/link1")
        self.assertEqual(evidence.email.urls[1].url_string, "https://example.com/link2")

    def test_create_email_evidence_with_size_string(self):
        """Test create_email_evidence with size as string."""
        entity_data = {
            "entityPayload": {
                "internetMessageId": "<<EMAIL>>",
                "subject": "Test Size String",
                "size": "12345",
            }
        }

        evidence = event_sync.create_email_evidence(entity_data)

        self.assertEqual(evidence.email.size, 12345)

    def test_create_email_evidence_with_size_int(self):
        """Test create_email_evidence with size as integer."""
        entity_data = {
            "entityPayload": {
                "internetMessageId": "<<EMAIL>>",
                "subject": "Test Size Int",
                "size": 67890,
            }
        }

        evidence = event_sync.create_email_evidence(entity_data)

        self.assertEqual(evidence.email.size, 67890)

    def test_create_email_evidence_with_invalid_size(self):
        """Test create_email_evidence with invalid size value."""
        entity_data = {
            "entityPayload": {
                "internetMessageId": "<<EMAIL>>",
                "subject": "Test Invalid Size",
                "size": "not_a_number",
            }
        }

        evidence = event_sync.create_email_evidence(entity_data)

        self.assertIsNone(evidence.email.size)

    def test_create_email_evidence_with_is_read_false(self):
        """Test create_email_evidence with isRead as false."""
        entity_data = {
            "entityPayload": {
                "internetMessageId": "<<EMAIL>>",
                "subject": "Test Unread",
                "isRead": "false",
            }
        }

        evidence = event_sync.create_email_evidence(entity_data)

        self.assertFalse(evidence.email.is_read)

    def test_create_email_evidence_with_empty_entity_data(self):
        """Test create_email_evidence with empty entity data."""
        entity_data = {}

        evidence = event_sync.create_email_evidence(entity_data)

        self.assertIsNotNone(evidence)
        self.assertIsNotNone(evidence.email)
        # All fields should be None or default values
        self.assertIsNone(evidence.email.message_uid)
        self.assertIsNone(evidence.email.subject)
        self.assertIsNone(evidence.email.from_mailbox)

    def test_create_email_evidence_with_empty_attachments(self):
        """Test create_email_evidence with empty attachments string."""
        entity_data = {
            "entityPayload": {
                "internetMessageId": "<<EMAIL>>",
                "subject": "Test Empty Attachments",
                "attachments": "",
            }
        }

        evidence = event_sync.create_email_evidence(entity_data)

        self.assertIsNone(evidence.email.files)

    def test_create_email_evidence_priority_to_over_recipients(self):
        """Test that 'to' field takes priority over 'recipients' field."""
        entity_data = {
            "entityPayload": {
                "internetMessageId": "<<EMAIL>>",
                "subject": "Test Priority",
                "to": "<EMAIL>",
                "recipients": "<EMAIL>",
                "cc": "<EMAIL>",
                "replyToEmail": "<EMAIL>",
            }
        }

        evidence = event_sync.create_email_evidence(entity_data)

        # Should use 'to' field, not 'recipients'
        self.assertEqual(evidence.email.to, ["<EMAIL>"])

    def test_create_email_evidence_fallback_to_recipients(self):
        """Test that 'recipients' field is used when 'to' is empty."""
        entity_data = {
            "entityPayload": {
                "internetMessageId": "<<EMAIL>>",
                "subject": "Test Fallback",
                "to": "",
                "recipients": "<EMAIL>",
            }
        }

        evidence = event_sync.create_email_evidence(entity_data)

        # Should use 'recipients' field as fallback
        self.assertEqual(evidence.email.to, ["<EMAIL>"])
