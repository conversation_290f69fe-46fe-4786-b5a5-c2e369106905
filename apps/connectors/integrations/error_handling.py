from datetime import timedel<PERSON>
from typing import List, Optional, Type

from pydantic import BaseModel
from requests import HTTPError

# This gives a total of 3 attempts, the first one plus the two retries.
DEFAULT_RETRY_ATTEMPTS = 2

# How long to wait (in seconds) before retrying a recoverable error.
DEFAULT_RETRY_COUNTDOWN = 60 * 5  # 5 minutes


class RetrySettings(BaseModel):
    retry_after: timedelta = timedelta(seconds=DEFAULT_RETRY_COUNTDOWN)
    max_retries: int = DEFAULT_RETRY_ATTEMPTS


class RetryOn(BaseModel):
    """Defines attributes about Exceptions that should be retried."""

    typ: Type[Exception]
    status_code: Optional[list[int]] = None

    # How to handle the retry for this error
    retry_settings: Optional[RetrySettings] = RetrySettings()

    def match(self, exception: Type[Exception]) -> bool:
        """Does this given exception match the retry criteria?"""
        if not isinstance(exception, self.typ):
            return False

        # Now, match any additional attributes
        if isinstance(exception, HTTPError) and self.status_code:
            return exception.response.status_code in self.status_code
        return True

    def raise_for_retry(self, ex: Type[Exception]) -> None:
        raise RecoverableError(str(ex), retry_settings=self.retry_settings) from ex


class RecoverableError(Exception):
    """A recoverable error that can be retried. Generally HTTP 429s and similar.
    The framework can raise this to indicate that the task should be retried.

    This is only used when the Actions are executing in a Celery task."""

    def __init__(
        self,
        message: str = "Recoverable error occurred, please retry later",
        retry_settings=RetrySettings(),
    ):
        super().__init__(message)
        self.retry_settings = retry_settings

    @classmethod
    def raise_if_recoverable(
        cls, exc: Type[Exception], recoverable_error_patterns: List[Type["RetryOn"]]
    ) -> None:
        """Raise a RecoverableError if the exception matches this retry criteria."""
        if to_retry := cls.is_recoverable_error(exc, recoverable_error_patterns):
            to_retry.raise_for_retry(exc)

    @classmethod
    def is_recoverable_error(
        cls, exc: Type[Exception], recoverable_error_patterns: List[RetryOn]
    ) -> RetryOn | None:
        """Given a list of recoverable error patterns, check if the exception matches any
        instance of the RetryOn conditions.

        The intent is that you can provide multiple Exception classes and also provide
        properties to check on the exception instance. If all of them match, then we
        consider the error recoverable.
        """
        for retry_matcher in recoverable_error_patterns:
            if retry_matcher.match(exc):
                return retry_matcher
        return None
