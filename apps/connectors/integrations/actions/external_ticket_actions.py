import logging
from datetime import datetime
from enum import StrEnum
from typing import List, Optional

from pydantic import EmailStr, HttpUrl

from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
    InvocationType,
)
from apps.connectors.integrations.actions.update_lifecycle_status import (
    CorrIncidentStatus,
)
from apps.connectors.integrations.schemas.action_args import IntegrationActionArgs

logger = logging.getLogger(__name__)


class StateUpdate(StrEnum):
    ASSIGNED_TO_CUSTOMER_ORG = "assigned_to_customer_org"
    ASSIGNED_TO_CUSTOMER_ORG_USER = "assigned_to_customer_org_user"
    ASSIGNED_TO_MONITORING_ORG = "assigned_to_monitoring_org"
    CLOSED = "closed"


class CommentFields(IntegrationActionArgs):
    comment_id: str
    created_at: datetime
    updated_at: datetime
    author_display_name: str
    author_email: EmailStr
    comment_md: str  # Markdown content of the comment
    attachment_filenames: List[
        str
    ] = None  # List of attachment filenames in the comment


class AttachmentFields(IntegrationActionArgs):
    attachment_id: str
    created_at: datetime
    file_name: str
    file_content: bytes  # Binary content of the file


class ExternalTicketFields(IntegrationActionArgs):
    ticket_id: str
    ticket_key: str
    created_at: datetime
    created_by_display_name: str
    created_by_email: EmailStr
    ticket_status: str
    ticket_state: Optional[StateUpdate] = None
    status_updated_at: datetime = None
    status_change_date: datetime = None
    comments: Optional[list[CommentFields]]  # List of comments on the ticket
    attachments: Optional[list[AttachmentFields]]  # List of attachments on the ticket
    raw_ticket: Optional[dict] = None  # Raw ticket data from the external system


class GetExternalTicketArgs(IntegrationActionArgs):
    ticket_id: str


class GetExternalTicket(IntegrationAction):
    name = "Get External Ticket"
    action_type = IntegrationActionType.GET_EXTERNAL_TICKET
    entitlement = Entitlement.internal_system
    invocation_type = InvocationType.PUBLIC
    metadata = IntegrationActionMetadata(
        args_type=GetExternalTicketArgs,
        result_type=ExternalTicketFields,
    )


class CreateExternalTicketArgs(IntegrationActionArgs):
    alert_id: str
    alert_title: str
    alert_organization_name: str
    alert_category: str
    alert_type: str
    alert_link: HttpUrl
    assigned_group: str
    integration_name: str
    soc_priority: str
    vendor_severity: str
    created_at: datetime
    assigned_user_display: str
    corr_status: CorrIncidentStatus


class CreateExternalTicketResult(IntegrationActionArgs):
    ticket_id: str
    ticket_key: str


class CreateExternalTicket(IntegrationAction):
    name = "Create External Ticket"
    action_type = IntegrationActionType.CREATE_EXTERNAL_TICKET
    entitlement = Entitlement.internal_system
    invocation_type = InvocationType.PUBLIC
    metadata = IntegrationActionMetadata(
        args_type=CreateExternalTicketArgs,
        result_type=CreateExternalTicketResult,
    )


class UpdateExternalTicketStatusArgs(IntegrationActionArgs):
    ticket_id: str
    state: StateUpdate


class UpdateExternalTicketStatusResult(IntegrationActionArgs):
    ...


class UpdateExternalTicketStatus(IntegrationAction):
    name = "Update External Ticket Status"
    action_type = IntegrationActionType.UPDATE_EXTERNAL_TICKET_STATUS
    entitlement = Entitlement.internal_system
    invocation_type = InvocationType.PUBLIC
    metadata = IntegrationActionMetadata(
        args_type=UpdateExternalTicketStatusArgs,
        result_type=UpdateExternalTicketStatusResult,
    )


class AddCommentArgs(IntegrationActionArgs):
    ticket_id: str
    author_display_name: str
    author_email: EmailStr
    comment: str


class AddCommentResult(IntegrationActionArgs):
    comment_id: str


class AddComment(IntegrationAction):
    name = "Add Comment to External Ticket"
    action_type = IntegrationActionType.ADD_COMMENT_TO_EXTERNAL_TICKET
    entitlement = Entitlement.internal_system
    invocation_type = InvocationType.PUBLIC
    metadata = IntegrationActionMetadata(
        args_type=AddCommentArgs,
        result_type=AddCommentResult,
    )


class AddAttachmentArgs(IntegrationActionArgs):
    ticket_id: str
    file_name: str
    content: bytes


class AddAttachmentResult(IntegrationActionArgs):
    attachment_id: str
    created_at: datetime
    file_name: str
    file_content_url: str
    author_display_name: str
    author_email: EmailStr


class AddAttachment(IntegrationAction):
    name = "Add Attachment to External Ticket"
    action_type = IntegrationActionType.ADD_ATTACHMENT_TO_EXTERNAL_TICKET
    entitlement = Entitlement.internal_system
    invocation_type = InvocationType.PUBLIC
    metadata = IntegrationActionMetadata(
        args_type=AddAttachmentArgs,
        result_type=AddAttachmentResult,
    )


class SearchExternalTicketArgs(IntegrationActionArgs):
    updated_at: datetime


class SearchExternalTicketResult(IntegrationActionArgs):
    search_result: Optional[List[ExternalTicketFields]] = None


class SearchExternalTicket(IntegrationAction):
    name = "Search External Tickets in Bulk"
    action_type = IntegrationActionType.SEARCH_EXTERNAL_TICKET
    entitlement = Entitlement.internal_system
    invocation_type = InvocationType.PUBLIC
    metadata = IntegrationActionMetadata(
        args_type=SearchExternalTicketArgs,
        result_type=SearchExternalTicketResult,
    )


class GetTransitionsArgs(IntegrationActionArgs):
    ticket_id: str


class GetTransitionResult(IntegrationActionArgs):
    transitions: List[dict]


class GetExternalTicketTransitions(IntegrationAction):
    name = "Get External Ticket Transitions"
    action_type = IntegrationActionType.GET_EXTERNAL_TICKET_TRANSITIONS
    entitlement = Entitlement.internal_system
    invocation_type = InvocationType.PUBLIC
    metadata = IntegrationActionMetadata(
        args_type=GetTransitionsArgs, result_type=GetTransitionResult
    )
