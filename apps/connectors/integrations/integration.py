import time
import uuid
from abc import ABC
from collections.abc import Generator, Iterator
from typing import Iterable

from pydantic import ValidationError
from requests.exceptions import ConnectionError, HTTPError

from apps.connectors.activity_logger import ActivityLogger, NullHandler
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionType,
)
from apps.connectors.integrations.actions.utils import NormalizationGenerator
from apps.connectors.integrations.bookmarks import TemplateVersionBookmarks
from apps.connectors.integrations.error_handling import (
    RecoverableError,
)
from apps.connectors.integrations.health_check import (
    IntegrationHealthCheck,
    IntegrationPermissionsHealthCheck,
)
from apps.connectors.integrations.settings import TemplateVersionSettings


class IntegrationError(Exception):
    pass


class InvokeTracer:
    """
    A logger class to track the start and stop times of an invocation,
    log messages, and handle errors.
    """

    UNKNOWN_ERROR = "unknown_error"

    def __init__(self, logger, invoke_label, func, ex_types, recoverable_exceptions):
        self.correlation_id = None
        self.invoke_label = invoke_label
        self.start_time = 0
        self.end_time = 0
        self.count = 0
        self.logger = logger
        self.ex = None
        self.normalization_error_count = 0
        self.func = func
        self.ex_types = ex_types
        self.recoverable_exceptions = recoverable_exceptions

    def start(self):
        """
        Starts the logger by setting the correlation ID and start time,
        and logs the start message.
        """
        self.correlation_id = str(uuid.uuid4())[:8]
        self.start_time = time.time()
        self.logger.info(
            f"{self.invoke_label} started. Correlation ID: {self.correlation_id};"
        )

    def stop(self):
        """
        Stops the logger by setting the end time, calculating the duration,
        and logging the appropriate message based on the status and count.
        """
        self.end_time = time.time()
        status = "failed" if self.ex else "completed"
        status_message = f"{self.invoke_label} {status}."
        elapsed_time = f"Elapsed time: {self.duration():.3f} seconds;"
        messages = [status_message, elapsed_time]
        if self.count:
            item_message = f"Items: {self.count:,};"
            messages.append(item_message)

        if self.normalization_error_count:
            item_message = f"Anomalies: {self.normalization_error_count:,};"
            messages.append(item_message)

        correlation_message = f"Correlation ID: {self.correlation_id};"
        messages.append(correlation_message)

        if self.ex:
            if self.ex == self.UNKNOWN_ERROR:
                err_message = "Error: An unknown error occurred.;"
            else:
                err_message = f"Error: {self.ex};"
            messages.insert(1, err_message)
            self.logger.error(" ".join(messages))
        else:
            self.logger.info(" ".join(messages))

    def increment(self):
        self.count += 1

    def increment_normalization_error(self):
        self.normalization_error_count += 1

    def duration(self):
        return self.end_time - self.start_time

    def set_error(self, ex):
        self.ex = ex

    def set_unknown_error(self):
        self.ex = self.UNKNOWN_ERROR

    def on_normalization_error(self, error, ix):
        self.increment_normalization_error()
        self.logger.log_normalization_error(error, ix)

    class GeneratorProxy(Iterator):
        def __init__(self, generator, tracer):
            self.generator = generator
            self.tracer = tracer

        def __iter__(self):
            return self

        def __next__(self):
            return self.tracer.try_call(self.generator.__next__)

    def result(self):
        self.start()
        result = self.try_call(self.func, is_start=True)

        if isinstance(result, Generator):
            generator = result
            if isinstance(generator, NormalizationGenerator):
                generator.on_error = self.on_normalization_error
            return self.GeneratorProxy(generator, self)

        self.stop()
        return result

    def try_call(self, func, is_start=False):
        try:
            result = func()
            if not is_start:
                self.increment()
            return result
        except StopIteration:
            self.stop()
            raise
        except self.ex_types as ex:
            self.set_error(ex)
            self.stop()
            if self.recoverable_exceptions:
                RecoverableError.raise_if_recoverable(
                    exc=ex,
                    recoverable_error_patterns=self.recoverable_exceptions,
                )
            raise IntegrationError(str(ex)) from ex
        except Exception:
            self.set_unknown_error()
            self.stop()
            raise


class IntegrationLogger(ActivityLogger):
    def __init__(self, handler, user_email=None):
        super().__init__(handler, user_email)

    def log_normalization_error(self, error, ix):
        messages = []
        match error:
            case ValidationError():
                for err in error.errors():
                    field_name = "__all__"
                    if loc := err.get("loc"):
                        field_name = ".".join(str(p) for p in loc)

                    msg = err.pop("msg", None)
                    messages.append(f"{field_name}: {msg}")
            case KeyError():
                error = str(error)
                error.strip()
                messages.append(f"{error} not found")
            case _:  # pragma: no cover
                messages.append(str(error))

        for message in messages:
            self.warning(f"Validation Item {ix}: {message}")

    def trace_call(self, label, func, ex_types, recoverable_exceptions=None):
        invoke_tracer = InvokeTracer(
            self, label, func, ex_types, recoverable_exceptions
        )
        return invoke_tracer.result()


class Integration(ABC):
    """
    Abstract class for all integrations. All integrations must inherit from this class.
    """

    api_class: type
    exception_types = ()
    actions: tuple[type[IntegrationAction], ...]
    critical_health_checks: tuple[type[IntegrationHealthCheck], ...] = ()
    logger: IntegrationLogger

    def __init__(
        self,
        config: dict,
        settings: TemplateVersionSettings,
        logger: IntegrationLogger | None = None,
        bookmarks: TemplateVersionBookmarks | None = None,
        connector_id: str = "unknown_connector_id",
    ):
        self.connector_id = connector_id
        self.config = config
        self.settings = settings
        self.bookmarks = bookmarks or {}
        self._actions_by_type = {action.action_type: action for action in self.actions}
        self.logger = logger or IntegrationLogger(NullHandler())

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        if not isinstance(cls.actions, Iterable):
            raise ValueError(
                f"{cls.__module__} - {cls.__name__}.actions must be an iterable"
            )

    def get_api(self):
        return self.api_class(**self.config)

    def invoke(self, method, **kwargs):
        """
        This method invokes the method and returns the result directly from the
        integration.

        NOTE: This is deprecated, use invoke_action instead.
        """
        api = self.get_api()
        func = lambda: getattr(api, method)(**kwargs)
        res = self.logger.trace_call(method, func, self.get_known_exception_types())
        if isinstance(res, Iterator):
            return list(res)
        return res

    def invoke_action(
        self,
        action_type: IntegrationActionType,
        action_args=None,
        bookmark=None,
        **kwargs,
    ):
        """
        This method invokes the action and returns the result.
        :param action_type: The action type to invoke.
        :param action_args: The arguments for the action.
        :return: The result of the action.
        """

        action = self.get_action(action_type)

        if not action_args:
            action_args = action.metadata.args_type(**kwargs)

        if bookmark is None:
            func = lambda: action.execute(action_args)
        else:
            func = lambda: action.execute(action_args, bookmark=bookmark)

        return self.logger.trace_call(
            action.name,
            func,
            self.get_known_exception_types(),
            action.recoverable_errors,
        )

    def poll_action(self, action_type, poll_context):
        """
        This method polls the status of the action.
        :param action_type: The action type to get the status of.
        :param poll_context: The poll result to get the status of.
        :return: The poll result or actual result of the action.
        """
        action = self.get_action(action_type)
        return action.poll(poll_context)

    def get_bookmark(self, action_type: IntegrationActionType):
        return self.bookmarks.get(action_type)

    def get_action(
        self,
        action_type: IntegrationActionType,
    ) -> IntegrationAction | None:
        action = self._actions_by_type.get(action_type)
        if action:
            return action(self, self.settings[action_type])

    def get_action_health_checks(
        self,
        action_type: IntegrationActionType,
    ) -> list[IntegrationPermissionsHealthCheck]:
        action = self.get_action(action_type)
        if action:
            return (
                [check(self) for check in action.get_permission_checks()]
                if action
                else []
            )
        return []

    def get_critical_health_checks(self) -> list[IntegrationHealthCheck]:
        return [check(self) for check in self.critical_health_checks]

    def get_action_result_type_name(self, action_type: IntegrationActionType):
        action = self.get_action(action_type)
        return action.metadata.result_type.__name__ if action.metadata else None

    def get_known_exception_types(self):
        return self.exception_types + (HTTPError, ConnectionError)
