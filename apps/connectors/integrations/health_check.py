from enum import StrEnum, auto

from apps.connectors.health_checks.base import HealthCheck


class IntegrationHealthCheckResult(StrEnum):
    PASSED = auto()
    FAILED = auto()
    MISCONFIGURED = auto()
    UNKNOWN = auto()


class IntegrationHealthCheckRequirementStatus(StrEnum):
    REQUIRED = auto()
    OPTIONAL = auto()


class IntegrationHealthCheck(HealthCheck):
    """
    Base class for health checks that are specific to an integration.
    """

    def __init__(self, integration):
        self.integration = integration

    def get_integration(self):
        return self.integration


class IntegrationConnectionHealthCheck(IntegrationHealthCheck):
    name = "Connection is valid"
    description = "Can connect to the integration API"
    value = None
    required = IntegrationHealthCheckRequirementStatus.REQUIRED


class ITSMIntegrationStatusHealthCheck(IntegrationHealthCheck):
    name = "ITSM Status's are valid"
    description = (
        "Integration is enabled and configured with correct ITSM status mappings"
    )
    value = None
    required = IntegrationHealthCheckRequirementStatus.REQUIRED


class IntegrationPermissionsHealthCheck(IntegrationHealthCheck):
    pass


class SimplePermissionsCheck(IntegrationPermissionsHealthCheck):
    allowed_values = []

    def _has_permission(self, permission: str) -> bool:
        return self.integration.invoke("has_permission", permission=permission)

    def get_result(self) -> IntegrationHealthCheckResult:
        from apps.connectors.integrations.integration import IntegrationError

        try:
            if any(
                self._has_permission(permission) for permission in self.allowed_values
            ):
                return IntegrationHealthCheckResult.PASSED
        except IntegrationError:
            pass

        return IntegrationHealthCheckResult.FAILED
