import uuid

from apps.connectors.integrations.api import ApiBase


class CheckPointHarmonyEmailV1Api(ApiBase):
    def __init__(self, url=None, client_id=None, access_key=None, **kwargs):
        self.base_url = url
        self.client_id = client_id
        self.access_key = access_key
        self._auth_token = None
        super().__init__(
            base_url=url,
            static_headers={
                "Content-Type": "application/json",
            },
        )

    @property
    def auth_session(self):
        """Get the current authentication token."""
        if self._auth_token is None:
            # If no token is set, try to get it from the API
            auth_payload = {"clientId": self.client_id, "accessKey": self.access_key}
            response = self.session.post(self.url("/auth/external"), json=auth_payload)
            if response.status_code == 200:
                token_data = response.json()
                self._auth_token = token_data.get("token")

        self.session.headers.update(
            {
                "Authorization": f"Bearer {self._auth_token}",
                "x-av-req-id": str(uuid.uuid4()),
            }
        )
        return self.session

    def query_events(self, start_date, end_date):
        params = {
            "startDate": start_date,
            "endDate": end_date,
        }
        response = self.auth_session.get(self.url("/event/query"), params=params)
        return response.json()

    def get_event_by_id(self, event_id):
        response = self.auth_session.get(self.url(f"/event/{event_id}"))
        return response.json()

    def quarantine_event(self, event_id):
        payload = {"eventId": event_id, "eventActionName": "quarantine"}
        response = self.auth_session.post(self.url("/v1.0/action/event"), json=payload)
        return response.json()

    def restore_event(self, event_id):
        payload = {"eventId": event_id, "eventActionName": "restore"}
        response = self.auth_session.post(self.url("/v1.0/action/event"), json=payload)
        return response.json()

    def get_task_status(self, task_id):
        response = self.auth_session.get(self.url(f"/v1.0/task/{task_id}"))
        return response.json()
