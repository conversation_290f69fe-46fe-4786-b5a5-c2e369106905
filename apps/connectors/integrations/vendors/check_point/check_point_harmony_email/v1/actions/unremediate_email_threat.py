from apps.connectors.integrations.actions.email_threat import (
    EmailThreatInfo,
    EmailThreatRemediationResult,
    EmailThreatRemediationStatus,
    MailMessage,
    UnRemediateEmailThreat,
)
from apps.connectors.integrations.schemas import (
    EmailThreatIdentifierArgs,
    IntegrationActionPollingContext,
)
from apps.connectors.integrations.vendors.check_point.check_point_harmony_email.v1.api import (
    CheckPointHarmonyEmailV1Api,
)


class CheckPointHarmonyEmailV1UnremediateEmailThreat(UnRemediateEmailThreat):
    def execute(self, args: EmailThreatIdentifierArgs) -> EmailThreatRemediationResult:
        event_id = args.email_threat_id.value

        api: CheckPointHarmonyEmailV1Api = self.integration.get_api()
        response = api.restore_event(event_id)

        task_id = response.get("responseData", {}).get("taskId")
        response = api.get_task_status(task_id)
        result = EmailThreatRemediationResult(
            result=EmailThreatInfo(
                technology_id="check_point_harmony_email",
                source_id=event_id,
                messages=[
                    MailMessage(
                        source_id=event_id,
                        technology_id="check_point_harmony_email",
                        internet_message_id=event_id,
                        subject=response.get("details", {}).get(
                            "message", "No message provided"
                        ),
                    )
                ],
                remediation_status=EmailThreatRemediationStatus.UNREMEDIATED,
            )
        )

        if task_id:
            result.result.remediation_status = EmailThreatRemediationStatus.IN_PROGRESS
        return result

    def poll(self, poll_context: IntegrationActionPollingContext):  # pragma: no cover
        # TODO: Implement polling logic
        return IntegrationActionPollingContext(context={})

    def get_permission_checks(self):  # pragma: no cover
        return []
