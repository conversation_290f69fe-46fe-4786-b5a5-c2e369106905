import json
from datetime import datetime, timezone
from typing import Dict, Generator, List

from dateutil import parser

from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventSync,
    EventSyncArgs,
    VendorRefExtended,
)
from apps.connectors.integrations.actions.utils import normalize
from apps.connectors.integrations.schemas.ocsf import (
    DetectionActivity,
    DetectionFinding,
    DetectionStatus,
    Email,
    EvidenceArtifacts,
    File,
    FindingInformation,
    Metadata,
    Product,
    Profile,
    Remediation,
    Severity,
    Url,
)
from apps.connectors.integrations.vendors.check_point.check_point_harmony_email.v1.api import (
    CheckPointHarmonyEmailV1Api,
)
from apps.connectors.integrations.vendors.check_point.check_point_harmony_email.v1.bookmarks import (
    CheckPointHarmonyEmailV1EventSyncBookmark,
)
from apps.connectors.utils.compound_id_helper import compound_id


def parse_datetime(dt_str: str) -> datetime:
    return parser.parse(dt_str)


def get_iso_date(dt_str: str) -> str:
    return parser.parse(dt_str).isoformat()


def map_severity(severity: str) -> Severity:
    severity_map = {
        "critical": Severity.CRITICAL,
        "highest": Severity.CRITICAL,
        "high": Severity.HIGH,
        "medium": Severity.MEDIUM,
        "low": Severity.LOW,
        "lowest": Severity.LOW,
        "info": Severity.INFORMATIONAL,
    }
    return severity_map.get(severity.lower(), Severity.UNKNOWN)


def map_status(state: str) -> DetectionStatus:
    status_map = {
        "new": DetectionStatus.NEW,
        "detected": DetectionStatus.NEW,
        "pending": DetectionStatus.IN_PROGRESS,
        "remediated": DetectionStatus.RESOLVED,
        "dismissed": DetectionStatus.SUPPRESSED,
        "exception": DetectionStatus.SUPPRESSED,
    }
    return status_map.get(state.lower(), DetectionStatus.UNKNOWN)


def map_confidence(confidence_indicator: str) -> int:
    confidence_map = {
        "malicious": 95,
        "suspicious": 75,
        "clean": 10,
        "unknown": 50,
    }
    return confidence_map.get(confidence_indicator.lower(), 50)


def parse_email_links(email_links: str) -> List[Url]:
    urls = []
    # Handle comma-separated URLs or single URL
    for url_str in email_links.split(","):
        url_str = url_str.strip()
        if url_str:
            urls.append(Url(url_string=url_str))
    return urls


def parse_json_field(field_value: str) -> Dict:
    return json.loads(field_value)


def extract_emails_from_user_field(user_field: str) -> List[str]:
    try:
        user_data = parse_json_field(user_field)
        if user_data and "mail" in user_data:
            return [user_data["mail"]]
    except:
        return user_field


def create_email_evidence(entity_data: Dict) -> EvidenceArtifacts:
    entity_payload = entity_data.get("entityPayload", {})

    # Parse attachments if present
    files = []
    attachments = entity_payload.get("attachments")
    if attachments and attachments != "":
        # Handle attachment data structure
        if isinstance(attachments, dict):
            for attachment in attachments.values():
                if isinstance(attachment, dict):
                    file_obj = File(
                        name=attachment.get("fileName"),
                        mime_type=attachment.get("mimeType"),
                        size=attachment.get("size"),
                    )
                    files.append(file_obj)
        elif isinstance(attachments, list):
            for attachment in attachments:
                if isinstance(attachment, dict):
                    file_obj = File(
                        name=attachment.get("fileName"),
                        mime_type=attachment.get("mimeType"),
                        size=attachment.get("size"),
                    )
                    files.append(file_obj)

    # Parse URLs from emailLinks
    urls = parse_email_links(entity_payload.get("emailLinks", ""))

    # Handle recipients fields according to Confluence mapping
    to_addresses = []
    cc_addresses = []

    # Handle 'to' field (can be plain email or recipients field)
    to_field = entity_payload.get("to", "") or entity_payload.get("recipients", "")
    if to_field:
        to_addresses.append(to_field)

    # Handle 'toUser' field (JSON string)
    to_user_emails = extract_emails_from_user_field(entity_payload.get("toUser", ""))
    to_addresses.extend(to_user_emails)

    # Handle 'cc' field
    cc_field = entity_payload.get("cc", "")
    if cc_field:
        cc_addresses.append(cc_field)

    # Handle 'ccUser' field (JSON string)
    cc_user_emails = extract_emails_from_user_field(entity_payload.get("ccUser", ""))
    cc_addresses.extend(cc_user_emails)

    # Handle reply-to field
    reply_to_email = entity_payload.get("replyToEmail")

    # Parse size field (can be string or int)
    size = None
    size_value = entity_payload.get("size")
    if size_value:
        try:
            size = int(size_value)
        except (ValueError, TypeError):
            size = None

    # Handle boolean fields (stored as strings in API)
    is_read = entity_payload.get("isRead") == "true"

    email = Email(
        message_uid=entity_payload.get("internetMessageId"),
        subject=entity_payload.get("subject"),
        from_mailbox=entity_payload.get("fromEmail"),
        to=to_addresses if to_addresses else None,
        cc=cc_addresses if cc_addresses else None,
        reply_to=reply_to_email if reply_to_email else None,
        size=size,
        files=files if files else None,
        urls=urls if urls else None,
        is_read=is_read,
    )

    return EvidenceArtifacts(email=email)


def convert_to_ocsf(event: Dict, entity_data: Dict) -> DetectionFinding:
    """Convert Check Point Harmony Email event to OCSF DetectionFinding following Confluence mapping."""

    # Create evidence artifacts from entity data if available
    evidences = []
    email_evidence = create_email_evidence(entity_data)
    if email_evidence:
        evidences.append(email_evidence)

    event_type = event.get("type", "").lower()

    # Create finding info with detailed description and title
    description = event.get("description", "")
    severity = event.get("severity", "Unknown")
    finding_info = FindingInformation(
        types=[event_type],
        desc=description,
        title=f"{event_type} Detection - {severity} Severity",
    )

    # Create product metadata according to Confluence mapping
    product = Product(
        name="Harmony Email and Collaboration",
        vendor_name="Check Point",
        version="1.0",
    )

    # Create metadata with proper UID mapping
    metadata = Metadata(
        correlation_uid=event.get("eventId"),
        uid=event.get("eventId"),
        product=product,
        profiles=[Profile.DATETIME, Profile.HOST, Profile.SECURITY_CONTROL],
        event_code=None,
    )

    # Parse remediation from available actions
    remediation = []
    available_actions = event.get("availableEventActions", [])
    if available_actions:
        for action in available_actions:
            if isinstance(action, dict):
                action_name = action.get("actionName", "")
                if action_name:
                    remediation.append(f"Available action: {action_name}")

    return DetectionFinding(
        activity=DetectionActivity.UNKNOWN,
        finding_info=finding_info,
        confidence=str(map_confidence(event.get("confidenceIndicator", ""))),
        severity=map_severity(event.get("severity", "")),
        status=map_status(event.get("state", "")),
        time_dt=get_iso_date(event.get("eventCreated")),
        message=description,
        metadata=metadata,
        evidences=evidences if evidences else None,
        remediation=Remediation(
            desc="\n".join(remediation) if remediation else None,
        ),
    )


def normalize_event(event: Dict) -> Event:
    entity_data = event.get("entity_data", {})
    event = event.get("event")
    return Event(
        event_timestamp=get_iso_date(event.get("eventCreated")),
        raw_event=event,
        ocsf=convert_to_ocsf(event, entity_data),
        vendor_item_ref=VendorRefExtended(
            id=compound_id.combine(
                event.get("eventId", ""), entity_data.get("entityId", "")
            ),
            title=event.get("eventId", ""),
        ),
    )


class CheckPointHarmonyEmailV1EventSync(EventSync):
    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: CheckPointHarmonyEmailV1EventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        api: CheckPointHarmonyEmailV1Api = self.integration.get_api()
        start_date = parse_datetime(bookmark.end_date)
        latest_receive_time = start_date

        end_date = datetime.now(timezone.utc).isoformat()

        for event in api.query_events(
            start_date=start_date.isoformat(), end_date=end_date
        )["responseData"]:
            event_time_str = event.get("eventCreated")
            event_id = event.get("eventId")
            for event_details in api.get_event_by_id(event_id)["responseData"]:
                yield {
                    "event": event,
                    "entity_data": event_details,
                }
                if event_time_str:
                    event_time = parse_datetime(event_time_str)
                    if event_time > latest_receive_time:
                        latest_receive_time = event_time

        # Update bookmark with latest seen event time
        bookmark.end_date = latest_receive_time.isoformat()

    def get_permission_checks(self):  # pragma: no cover
        return []  # Add specific permission checks if needed
