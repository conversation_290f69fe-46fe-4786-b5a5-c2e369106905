from apps.connectors.integrations import TemplateVersion

from .bookmarks import CheckPointHarmonyEmailV1Bookmarks
from .connection import (
    CheckPointHarmonyEmailV1Config,
    CheckPointHarmonyEmailV1Connection,
)
from .integration import CheckPointHarmonyEmailV1Integration
from .settings import CheckPointHarmonyEmailV1Settings


class CheckPointHarmonyEmailV1TemplateVersion(TemplateVersion):
    integration = CheckPointHarmonyEmailV1Integration
    id = "v1"
    name = "v1"
    config_model = CheckPointHarmonyEmailV1Config
    settings_model = CheckPointHarmonyEmailV1Settings
    connection_model = CheckPointHarmonyEmailV1Connection
    bookmarks_model = CheckPointHarmonyEmailV1Bookmarks
