from pydantic import Field, HttpUrl

from apps.connectors.integrations import ConnectionTemplate, TemplateVersionConfig
from apps.connectors.integrations.types import EncryptedStr


class CheckPointHarmonyEmailV1Config(TemplateVersionConfig):
    url: HttpUrl = Field(
        title="API Endpoint",
        description="API Endpoint used to communicate Abnormal Security.",
    )
    client_id: str = Field(
        title="Client ID",
        description="Client ID used to authenticate with Check Point Harmony Email and Collaboration.",
    )
    access_key: EncryptedStr = Field(
        title="Access Key",
        description="Access Key used to authenticate with Check Point Harmony Email and Collaboration.",
    )


class CheckPointHarmonyEmailV1Connection(ConnectionTemplate):
    id = "check_point_harmony_email"
    name = "Check Point Harmony Email and Collaboration"
    config_model = CheckPointHarmonyEmailV1Config
