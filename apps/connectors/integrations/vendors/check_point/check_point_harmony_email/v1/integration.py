from apps.connectors.integrations import Integration

from .actions.event_sync import CheckPointHarmonyEmailV1EventSync
from .actions.remediate_email_threat import CheckPointHarmonyEmailV1RemediateEmailThreat
from .actions.unremediate_email_threat import (
    CheckPointHarmonyEmailV1UnremediateEmailThreat,
)
from .api import CheckPointHarmonyEmailV1Api
from .health_check import ConnectionHealthCheck


class CheckPointHarmonyEmailV1Integration(Integration):
    api_class = CheckPointHarmonyEmailV1Api
    actions = (
        CheckPointHarmonyEmailV1EventSync,
        CheckPointHarmonyEmailV1UnremediateEmailThreat,
        CheckPointHarmonyEmailV1RemediateEmailThreat,
    )
    critical_health_checks = (<PERSON><PERSON>ealth<PERSON>he<PERSON>,)
