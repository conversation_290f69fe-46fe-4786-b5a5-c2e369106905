from datetime import datetime, timedelta, timezone

from pydantic import Field

from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.bookmarks import create_bookmarks_model
from apps.connectors.integrations.template import TemplateVersionActionBookmark


def default_latest_time_remediated():
    dt = datetime.now(timezone.utc) - timedelta(days=1)
    return dt.strftime("%Y-%m-%dT%H:%M:%SZ")


class CheckPointHarmonyEmailV1EventSyncBookmark(TemplateVersionActionBookmark):
    end_date: str = Field(
        title="Latest Event Update Datetime",
        description="The latest received during a fetch.",
        default_factory=default_latest_time_remediated,
    )


CheckPointHarmonyEmailV1Bookmarks = create_bookmarks_model(
    "CheckPointHarmonyEmailV1Bookmarks",
    {
        IntegrationActionType.EVENT_SYNC: CheckPointHarmonyEmailV1EventSyncBookmark,
    },
)
