from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.vendor import Vendors

from .v1 import CheckPointHarmonyEmailV1TemplateVersion


class CheckPointHarmonyEmailTemplate(Template):
    id = "check_point_harmony_email"
    name = "Check Point Harmony Email and Collaboration"
    category = Template.Category.EMAIL_SECURITY
    versions = {
        CheckPointHarmonyEmailV1TemplateVersion.id: CheckPointHarmonyEmailV1TemplateVersion(),
    }
    vendor = Vendors.CHECK_POINT
