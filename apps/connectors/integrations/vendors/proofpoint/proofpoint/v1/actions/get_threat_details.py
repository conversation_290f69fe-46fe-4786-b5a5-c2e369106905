from typing import Optional

from apps.connectors.integrations.actions.threat_intelligence import (
    GetThreatDetails,
    ThreatDetailsResult,
)
from apps.connectors.integrations.schemas import EmailThreatIdentifierArgs
import apps.connectors.integrations.schemas.ocsf as ocsf
from apps.connectors.integrations.vendors.proofpoint.proofpoint.v1.api import (
    ProofpointV1Api,
)


def map_threat_type_to_osint_type(threat_type: str) -> ocsf.OSINTIndicatorType:
    """
    Map Proofpoint threat types to OCSF OSINT indicator types.

    Args:
        threat_type: Proofpoint threat type (ATTACHMENT, URL, MESSAGE, etc.)

    Returns:
        Corresponding OCSF OSINTIndicatorType enum value
    """
    threat_type_upper = threat_type.upper() if threat_type else ""

    mapping = {
        "ATTACHMENT": ocsf.OSINTIndicatorType.FILE,
        "URL": ocsf.OSINTIndicatorType.URL,
        "MESSAGE": ocsf.OSINTIndicatorType.EMAIL,
        "HASH": ocsf.OSINTIndicatorType.HASH,
        "FILE": ocsf.OSINTIndicatorType.FILE,
    }

    return mapping.get(threat_type_upper, ocsf.OSINTIndicatorType.OTHER)


def map_threat_status_to_confidence(threat_status: str) -> ocsf.Confidence:
    """
    Map Proofpoint threat status to OCSF confidence levels.

    Args:
        threat_status: Proofpoint threat status (active, cleared, falsePositive)

    Returns:
        Corresponding OCSF Confidence enum value
    """
    status_lower = threat_status.lower() if threat_status else ""

    mapping = {
        "active": ocsf.Confidence.HIGH,
        "cleared": ocsf.Confidence.MEDIUM,
        "falsepositive": ocsf.Confidence.LOW,
        "false_positive": ocsf.Confidence.LOW,
    }

    return mapping.get(status_lower, ocsf.Confidence.UNKNOWN)


def normalize_threat_actors(actors_data: list) -> Optional[str]:
    """
    Normalize Proofpoint actor data to a comment string.

    Since OSINT doesn't have a specific threat_actor field, we'll include
    actor information in the comment field for analyst reference.

    Args:
        actors_data: List of actor objects from Proofpoint API

    Returns:
        Formatted actor information string or None if no valid actor data
    """
    if not actors_data or not isinstance(actors_data, list):
        return None

    actor_names = []
    for actor in actors_data:
        if isinstance(actor, dict) and actor.get("name"):
            actor_names.append(actor.get("name"))

    if actor_names:
        return f"Associated threat actors: {', '.join(actor_names)}"

    return None


def normalize_threat_to_osint(threat_data: dict) -> ocsf.OSINT:
    """
    Convert Proofpoint threat summary data to OCSF OSINT format.

    Args:
        threat_data: Raw threat data from Proofpoint API

    Returns:
        OSINT object with normalized threat intelligence data
    """
    threat_id = threat_data.get("threatId") or threat_data.get("threatID")
    threat_type = threat_data.get("threatType", "")
    classification = threat_data.get("classification", "")
    threat_status = threat_data.get("threatStatus", "")
    threat_url = threat_data.get("threatUrl")
    campaign_id = threat_data.get("campaignId")
    actors = threat_data.get("actors", [])
    threat_value = threat_data.get("threat", threat_id)

    # Map Proofpoint fields to OCSF OSINT
    osint_type = map_threat_type_to_osint_type(threat_type)
    confidence = map_threat_status_to_confidence(threat_status)

    # Create campaign object if campaign ID exists
    campaign = None
    if campaign_id:
        campaign = ocsf.Campaign(name=campaign_id)

    # Normalize threat actors to comment
    actor_comment = normalize_threat_actors(actors)

    # Build comprehensive comment with threat details
    comment_parts = []
    if classification:
        comment_parts.append(f"Classification: {classification}")
    if threat_status:
        comment_parts.append(f"Status: {threat_status}")
    if threat_type:
        comment_parts.append(f"Type: {threat_type}")
    if actor_comment:
        comment_parts.append(actor_comment)

    comment = "; ".join(comment_parts) if comment_parts else None

    return ocsf.OSINT(
        uid=threat_id,
        value=threat_value or threat_id or "",
        type_id=osint_type.id,
        category=classification or None,
        confidence_id=confidence.id,
        src_url=threat_url,
        vendor_name="Proofpoint",
        campaign=campaign,
        comment=comment,
    )


class ProofpointV1GetThreatDetails(GetThreatDetails):
    """
    Proofpoint implementation for retrieving detailed threat intelligence.

    This action fetches comprehensive threat details from Proofpoint's
    threat summary API and normalizes the data into OCSF OSINT format
    for standardized threat intelligence consumption.

    Features:
    - Retrieves threat details via /v2/threat/summary/{threat_id}
    - Maps Proofpoint threat types to OCSF indicator types
    - Converts threat status to confidence levels
    - Includes campaign and actor information
    - Provides standardized OSINT output for SOC workflows
    """

    def execute(self, args: EmailThreatIdentifierArgs) -> ThreatDetailsResult:
        """
        Execute threat details retrieval for a given threat ID.

        Args:
            args: Arguments containing the email threat identifier

        Returns:
            ThreatDetailsResult containing normalized OSINT data

        Raises:
            IntegrationError: If API call fails or threat not found
        """
        api: ProofpointV1Api = self.integration.get_api()
        threat_id = args.email_threat_id.value

        # Fetch threat details from Proofpoint API
        threat_response = api.get_email_threat(threat_id)

        # Normalize the response to OCSF OSINT format
        osint_data = normalize_threat_to_osint(threat_response)

        return ThreatDetailsResult(result=osint_data)

    def get_permission_checks(self): # pragma: no cover
        """Return permission checks required for this action."""
        return []  # No specific permission checks required
