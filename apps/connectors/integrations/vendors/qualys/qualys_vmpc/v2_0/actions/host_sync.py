from typing import Generator

from apps.connectors.integrations.actions.host_sync import (
    AssetCriticality,
    Host,
    HostSync,
    HostSyncArgs,
    InternetExposure,
    VulnerabilityAssetSync,
)
from apps.connectors.integrations.actions.utils import normalize, normalize_last_seen
from apps.connectors.integrations.vendors.qualys.qualys_vmpc.v2_0.api import paginate
from apps.connectors.integrations.vendors.qualys.qualys_vmpc.v2_0.health_check import (
    ReadHosts,
)
from apps.connectors.integrations.vendors.qualys.qualys_vmpc.v2_0.retry import (
    RetryOnQualysRateLimit,
)

INTERNET_FACING_TAG_NAME = "Internet Facing Assets"


criticality_map = {
    "1": AssetCriticality.TIER_4,
    "2": AssetCriticality.TIER_3,
    "3": AssetCriticality.TIER_2,
    "4": AssetCriticality.TIER_1,
    "5": AssetCriticality.TIER_0,
}


def normalize_host(host_data: dict):
    dns_data = host_data.get("DNS_DATA", {})
    hostname = dns_data.get("HOSTNAME") or host_data.get("NETBIOS") or ""
    fqdn = [dns_data.get("FQDN")] or []
    group_names = (
        host_data["ASSET_GROUP_TITLES"].split(",")
        if host_data.get("ASSET_GROUP_TITLES")
        else []
    )

    ips = [host_data["IP"]] if host_data.get("IP") else []

    os_name = host_data.get("OS", host_data.get("os", ""))

    criticality = criticality_map.get(
        host_data.get("ASSET_CRITICALITY_SCORE"), AssetCriticality.UNKNOWN
    )

    tags = host_data["TAGS"].get("TAG", []) if host_data.get("TAGS") else []
    is_internet_facing = any(tag["NAME"] == INTERNET_FACING_TAG_NAME for tag in tags)
    internet_exposure = (
        InternetExposure.INTERNET_FACING
        if is_internet_facing
        else InternetExposure.NOT_INTERNET_FACING
    )

    return Host(
        source_id=host_data["ID"],
        group_names=group_names,
        hostname=hostname,
        fqdns=fqdn,
        ip_addresses=ips,
        is_internet_facing=is_internet_facing,
        internet_exposure=internet_exposure,
        _os_name=os_name,
        criticality=criticality,
        last_seen=normalize_last_seen(
            [
                host_data.get("LAST_ACTIVITY"),
                host_data.get("LAST_BOOT"),
                host_data.get("LAST_VULN_SCAN_DATETIME"),
                host_data.get("LAST_VM_SCANNED_DATE"),
                host_data.get("LAST_VM_AUTH_SCANNED_DATE"),
            ]
        ),
        source_data=host_data,
    )


def fetch_hosts(api, params):
    group_id_to_title = {}
    for page in paginate(api.list_hosts, **params):
        hosts = list(page)
        groups_to_fetch = set()
        for host in hosts:
            if groups := host.get("ASSET_GROUP_IDS"):
                missing_groups = set(groups.split(",")) - set(group_id_to_title)
                groups_to_fetch.update(missing_groups)

        if groups_to_fetch:
            # Groups have much less data than hosts,
            # so we should be able to fetch all of them in one request.
            group_params = {
                "ids": ",".join(groups_to_fetch),
                "show_attributes": "TITLE",
                "truncation_limit": 0,
            }
            for group_page in paginate(api.list_groups, **group_params):
                for group in group_page:
                    group_id_to_title[group["ID"]] = group["TITLE"]

        for host in hosts:
            if groups := host.get("ASSET_GROUP_IDS"):
                host["ASSET_GROUP_TITLES"] = ",".join(
                    [group_id_to_title[group_id] for group_id in groups.split(",")]
                )
            yield host


class QualysVmpcV20HostSync(HostSync):
    recoverable_errors = [RetryOnQualysRateLimit()]

    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        # we are using default truncation_limit=1000
        params = {
            "show_asset_id": 1,
            "show_tags": 1,
            "host_metadata": "all",
            "show_cloud_tags": 1,
            "show_trurisk": 1,
        }
        if self.settings.fetch_by_tags:
            params["use_tags"] = 1
            params["tag_set_by"] = "name"
            params["tag_set_include"] = self.settings.fetch_by_tags
        if self.settings.fetch_asset_groups:
            params["details"] = "All/AGs"
        else:
            params["details"] = "All"

        api = self.integration.get_api()
        yield from fetch_hosts(api, params)

    def get_permission_checks(self):
        return [ReadHosts]


from apps.connectors.integrations.vendors.qualys.qualys_vmpc.v2_0.retry import (
    RetryOnQualysRateLimit,
)


class QualysVmpcV20VulnerabilityAssetSync(VulnerabilityAssetSync):
    recoverable_errors = [RetryOnQualysRateLimit()]

    @normalize(normalize_host)
    def execute(self, args: HostSyncArgs, **kwargs) -> Generator[Host, None, None]:
        # we are using default truncation_limit=1000
        params = {
            "show_asset_id": 1,
            "show_tags": 1,
            "host_metadata": "all",
            "show_cloud_tags": 1,
            "show_trurisk": 1,
            "details": "All",
        }
        api = self.integration.get_api()
        yield from fetch_hosts(api, params)

    def get_permission_checks(self):
        return [ReadHosts]  # pragma: no cover
