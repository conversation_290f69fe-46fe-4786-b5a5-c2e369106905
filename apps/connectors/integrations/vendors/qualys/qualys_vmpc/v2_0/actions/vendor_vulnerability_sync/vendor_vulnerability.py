import json
import logging
import os
import tempfile
from typing import Generator

from bs4 import BeautifulSoup

from apps.connectors.integrations.actions import normalize
from apps.connectors.integrations.actions.vendor_vulnerability_sync import (
    VendorVulnerability,
    VendorVulnerabilitySync,
    VendorVulnerabilitySyncArgs,
)
from apps.connectors.integrations.vendors.qualys.qualys_vmpc.v2_0.health_check import (
    ReadKnowledgeBase,
)
from apps.connectors.integrations.vendors.qualys.qualys_vmpc.v2_0.retry import (
    RetryOnQualysRateLimit,
)

logger = logging.getLogger(__name__)


def html_to_markdown(html: str) -> str:
    """
    Convert HTML to markdown with links.
    """
    if not html:
        return ""

    try:
        soup = BeautifulSoup(html, "html.parser")

        # convert all links to markdown format
        for a in soup.find_all("a"):
            a.replace_with(f"[{a.text}]({a['href']})")

        # Add new lines at paragraph tags
        for p in soup.find_all("p"):
            p.insert_before("\n")

        # add new lines at break tags
        for br in soup.find_all("br"):
            br.insert_after("\n")

        md_text = soup.get_text()

        # check if the text if one of the various forms of N/A
        # and return an empty string if it is
        if len(md_text) < 20:  # only bother to check short strings
            translation_table = str.maketrans("", "", " \n/")
            text_to_check = md_text.translate(translation_table).lower()
            if text_to_check == "na":
                return ""
    except Exception:  # pragma: no cover
        logger.exception(f"Error converting HTML to markdown")
        md_text = ""

    return md_text


def normalize_vendor_vulnerability(vendor_vuln_data: dict) -> VendorVulnerability:
    source_id = vendor_vuln_data["QID"]
    technology_id = "qualys_vmpc"
    title = vendor_vuln_data.get("TITLE")

    # If you encounter a situation where CVSS (and CVSS_V3) are not being returned
    # by the API, check in the Qualys console for...
    #   VMDR->Reports->Setup->CVSS Scoring->Show CVSS Scoring and ensure it is enabled

    def get_cvss_base_score(cvss_data: dict):
        if not cvss_data:
            return None

        base = cvss_data.get("BASE")

        # handle the "normal" format we see
        # <BASE>8.6</BASE >
        score = None
        if base and isinstance(base, str):
            score = base
        elif base and isinstance(base, dict):
            # handle the other format we sometimes see
            # <BASE source="service">5.4</BASE>
            score = base.get("#text", None)

        return score

    cvss_v2_base_score = get_cvss_base_score(vendor_vuln_data.get("CVSS"))
    cvss_v3_base_score = get_cvss_base_score(vendor_vuln_data.get("CVSS_V3"))

    cves = []
    for cve_entry in vendor_vuln_data.get("CVE_LIST", {}).get("CVE", []):
        cves.append(cve_entry["ID"])

    patchable = vendor_vuln_data.get("PATCHABLE", False)

    # convert the solution from HTML to text
    solution_html = vendor_vuln_data.get("SOLUTION", None)
    solution = html_to_markdown(solution_html)

    return VendorVulnerability(
        source_id=source_id,
        technology_id=technology_id,
        title=title,
        cves=cves,
        patchable=patchable,
        solution=solution,
        cvss_v3_base_score=cvss_v3_base_score,
        cvss_v2_base_score=cvss_v2_base_score,
    )


class QualysVmpcV20VendorVulnerabilitySync(VendorVulnerabilitySync):
    recoverable_errors = [RetryOnQualysRateLimit()]

    @normalize(normalize_vendor_vulnerability)
    def execute(
        self, args: VendorVulnerabilitySyncArgs, **kwargs
    ) -> Generator[VendorVulnerability, None, None]:
        params = {"details": "All"}

        if args.since:
            params["last_modified_after"] = args.since.strftime("%Y-%m-%dT%H:%M:%SZ")

        logger.info(f"Fetching vendor vulnerabilities", extra=params)

        api = self.integration.get_api()

        # Write the vendor vulns to a temp file.
        # We do this because the normalize method includes HTML parsing
        # which can be slow the processing enough to cause the
        # Qualys API call to timeout.
        try:
            temp_file_name = None
            with tempfile.NamedTemporaryFile(delete=False) as tf:
                temp_file_name = tf.name

            self._write_to_file(
                api.list_knowledge_base_vuln_stream(**params),
                temp_file_name,
            )

            yield from self._read_from_file(temp_file_name)

        finally:
            # delete the temp file
            os.remove(tf.name)

    def get_permission_checks(self):
        return [ReadKnowledgeBase]

    def _write_to_file(self, vendor_vulns: Generator, file_name: str):
        """
        Write the vendor vulnerabilities to a file.
        """
        with open(file_name, "a") as tf:
            for detected_vuln in vendor_vulns:
                data = json.dumps(detected_vuln, default=str) + "\n"
                tf.write(data)

    def _read_from_file(self, file_name: str):
        """
        Read the vendor vulnerabilities from a file.
        """
        with open(file_name, "r") as f:
            for line in f:
                yield json.loads(line)
