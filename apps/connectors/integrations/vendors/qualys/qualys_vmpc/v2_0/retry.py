from typing import Optional, Type
from xml.etree.ElementTree import fromstring

from requests import HTTPError

from apps.connectors.integrations.error_handling import (
    RecoverableError,
    RetryOn,
    RetrySettings,
)


class RetryOnQualysRateLimit(RetryOn):
    """Specialized retry handler which uses the Qualys API error response to
    determine retry settings
    """

    typ: Type[HTTPError] = HTTPError

    # Qualys uses 409 Conflict for rate limiting and concurrency issues.
    # See here for more details: https://cdn2.qualys.com/docs/qualys-api-limits.pdf
    status_code: Optional[list[int]] = [409]

    def raise_for_retry(self, ex: HTTPError) -> None:
        """This method does not perform additional validation - it's assumed that
        the framework has validated the attributes of the exception match"""
        parsed = fromstring(ex.response.text)

        error_code = int(parsed.findtext(".//CODE"))
        retry_settings = self.retry_settings
        if error_code == 1965:
            # Rate Limit Exceeded
            seconds_to_wait = parsed.findtext('.//ITEM[KEY="SECONDS_TO_WAIT"]/VALUE')
            retry_settings = RetrySettings(retry_after=int(seconds_to_wait))
        elif error_code == 1960:
            # Concurrency Limit - in this case we just use the default retry settings
            pass

        raise RecoverableError(str(ex), retry_settings=retry_settings) from ex
