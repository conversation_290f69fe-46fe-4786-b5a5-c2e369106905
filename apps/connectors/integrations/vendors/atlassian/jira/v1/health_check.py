from requests import HTTPError

from apps.connectors.integrations.health_check import (
    IntegrationConnectionHealthCheck,
    IntegrationHealthCheckResult,
    ITSMIntegrationStatusHealthCheck,
)


class ConnectionHealthCheck(IntegrationConnectionHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        try:
            jql = "ORDER BY created DESC"
            api.search_issues_bulk(jql, max_results=1)
            return IntegrationHealthCheckResult.PASSED
        except HTTPError:
            return IntegrationHealthCheckResult.FAILED


class StatusHealthCheck(ITSMIntegrationStatusHealthCheck):
    def get_result(self) -> IntegrationHealthCheckResult:
        api = self.integration.get_api()
        try:
            statuses = api.get_all_statuses()
            if not statuses:
                return IntegrationHealthCheckResult.MISCONFIGURED
            return IntegrationHealthCheckResult.PASSED
        except HTTPError:
            return IntegrationHealthCheckResult.FAILED
