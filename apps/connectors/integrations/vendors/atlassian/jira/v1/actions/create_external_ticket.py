from apps.connectors.integrations.actions.external_ticket_actions import (
    CreateExternalTicket,
    CreateExternalTicketArgs,
    CreateExternalTicketResult,
)
from apps.connectors.integrations.vendors.atlassian.jira.utils import get_issue_fields
from apps.connectors.integrations.vendors.atlassian.jira.v1.settings import (
    JiraCreateIssueSettings,
)


class JiraV1CreateIssue(CreateExternalTicket):
    settings = JiraCreateIssueSettings

    def execute(self, args: CreateExternalTicketArgs) -> CreateExternalTicketResult:
        api = self.integration.get_api()
        issue_fields = get_issue_fields(
            alert_fields=args.model_dump(),
            field_settings=self.settings.model_dump(),
            project_key=self.settings.project_key,
            issue_type=self.settings.issue_type,
        )
        response = api.create_issue(issue_fields=issue_fields)
        return CreateExternalTicketResult(
            ticket_id=response.get("id"),
            ticket_key=response.get("key"),
        )

    def get_permission_checks(self):
        return []
