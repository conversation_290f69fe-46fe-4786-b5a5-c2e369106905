from apps.connectors.integrations.actions.external_ticket_actions import (
    StateUpdate,
    UpdateExternalTicketStatus,
    UpdateExternalTicketStatusArgs,
    UpdateExternalTicketStatusResult,
)
from apps.connectors.integrations.vendors.atlassian.jira.v1.settings import (
    JiraUpdateStatusSettings,
)


class JiraV1UpdateIssueStatus(UpdateExternalTicketStatus):
    settings = JiraUpdateStatusSettings

    def execute(
        self, args: UpdateExternalTicketStatusArgs, **kwargs
    ) -> UpdateExternalTicketStatusResult:
        self.validate_status_mapping()

        api = self.integration.get_api()
        jira_status = self.get_jira_status_mapping(args.state)
        api.update_issue_status(issue_id_or_key=args.ticket_id, status=jira_status)

        return UpdateExternalTicketStatusResult()

    def get_permission_checks(self):
        return []

    def validate_status_mapping(self):
        """
        Validate the status mapping provided in settings against the Jira API.
        """
        api = self.integration.get_api()
        all_statuses = api.get_all_statuses().values()
        status_mappings = [
            self.settings.status_mapping_closed,
            self.settings.status_mapping_assigned_to_monitoring_org,
            self.settings.status_mapping_assigned_to_customer_org,
            self.settings.status_mapping_assigned_to_customer_org_user,
        ]

        for mapping in status_mappings:
            if mapping not in all_statuses:
                raise ValueError(
                    f"Status mapping '{mapping}' is not valid. "
                    "Please check the status mapping in the integration settings."
                )

    def get_jira_status_mapping(self, state: str) -> str:
        """
        Get the Jira status for a given transition.
        """
        if state == StateUpdate.ASSIGNED_TO_CUSTOMER_ORG:
            return self.settings.status_mapping_assigned_to_customer_org
        if state == StateUpdate.ASSIGNED_TO_CUSTOMER_ORG_USER:
            return self.settings.status_mapping_assigned_to_customer_org_user
        if state == StateUpdate.ASSIGNED_TO_MONITORING_ORG:
            return self.settings.status_mapping_assigned_to_monitoring_org
        if state == StateUpdate.CLOSED:
            return self.settings.status_mapping_closed
