from typing import Optional

from pydantic import ConfigDict, Field

from apps.connectors.integrations import create_settings_model
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.template import TemplateVersionActionSettings


class JiraCreateIssueSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(title="Jira Create Issue Settings")

    project_key: str = Field(
        title="Project Key",
        description="The key of the Jira project where issues will be created.",
        default="",
    )
    issue_type: str = Field(
        title="Issue Type",
        description="The Issue Type ID to create in Jira.",
        default="",
    )

    alert_id: Optional[str] = Field(
        title="Alert ID",
        description="Jira field to map the alert ID to. (Use dot notation for nested fields)",
        default="",
    )
    alert_title: Optional[str] = Field(
        title="Alert Title",
        description="Jira field to map the alert title to. (Use dot notation for nested fields)",
        default="",
    )
    alert_organization_name: Optional[str] = Field(
        title="Alert Organization",
        description="Jira field to map the alert's organization to. (Use dot notation for nested fields)",
        default="",
    )
    alert_category: Optional[str] = Field(
        title="Alert Category",
        description="Jira field to map the alert category to. (Use dot notation for nested fields)",
        default="",
    )
    alert_type: Optional[str] = Field(
        title="Alert Type",
        description="Jira field to map the alert's alert type to. (Use dot notation for nested fields)",
        default="",
    )
    alert_link: Optional[str] = Field(
        title="Alert Link",
        description="Jira field to map the alert's CORR link to. (Use dot notation for nested fields)",
        default="",
    )
    assigned_group: Optional[str] = Field(
        title="Assigned Group",
        description="Jira field to map the alert's assigned group to. (Use dot notation for nested fields)",
        default="",
    )
    integration_name: Optional[str] = Field(
        title="Integration Name",
        description="Jira field to map the alert's integration name to. (Use dot notation for nested fields)",
        default="",
    )
    soc_priority: Optional[str] = Field(
        title="SOC Priority",
        description="Jira field to map the alert's SOC priority to. (Use dot notation for nested fields)",
        default="",
    )
    vendor_severity: Optional[str] = Field(
        title="Vendor Severity",
        description="Jira field to map the alert's vendor severity to. (Use dot notation for nested fields)",
        default="",
    )
    alert_created_at: Optional[str] = Field(
        title="Alert Created At",
        description="Jira field to map the alert's creation date to. (Use dot notation for nested fields)",
        default="",
    )
    assigned_user_display: Optional[str] = Field(
        title="Assigned User",
        description="Jira field to map the alert's assigned user to. (Use dot notation for nested fields)",
        default="",
    )
    corr_status: Optional[str] = Field(
        title="CORR Status",
        description="Jira field to map the alert's CORR status to. (Use dot notation for nested fields)",
        default="",
    )


class JiraUpdateStatusSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(title="Jira Update Issue Status Settings")

    status_mapping_assigned_to_customer_org: str = Field(
        title="Customer Org assignment Status",
        description="Status to transition in Jira for alerts assigned to the customer org. ex. New",
        default="",
    )
    status_mapping_assigned_to_customer_org_user: Optional[str] = Field(
        title="Customer Org user assignment Status",
        description="Status to transition to in Jira for alerts assigned to a customer user. ex. In Progress",
        default="",
    )
    status_mapping_assigned_to_monitoring_org: str = Field(
        title="Monitoring Org assignment Status",
        description="Status to transition to in Jira for alerts assigned to CriticalStart. ex. Awaiting Vendor",
        default="",
    )
    status_mapping_closed: str = Field(
        title="Closed Status",
        description="Status to transition to in Jira for closed alerts. ex. Resolved",
        default="",
    )


class JiraSearchBulkIssuesSettings(TemplateVersionActionSettings):
    model_config = ConfigDict(title="Jira Update Issue Status Settings")

    project_key: str = Field(
        title="Project Key",
        description="The key of the Jira project to search for issues.",
        default="",
    )
    user_email: str = Field(
        title="User Email",
        description="The user account email to be used for this integration.",
        default="",
    )

    status_mapping_assigned_to_customer_org: str = Field(
        title="Customer Org assignment Status",
        description="Status to transition in Jira for alerts assigned to the customer org. ex. New",
        default="",
    )
    status_mapping_assigned_to_customer_org_user: Optional[str] = Field(
        title="Customer Org user assignment Status",
        description="Status to transition to in Jira for alerts assigned to a customer user. ex. In Progress",
        default="",
    )
    status_mapping_assigned_to_monitoring_org: str = Field(
        title="Monitoring Org assignment Status",
        description="Status to transition to in Jira for alerts assigned to CriticalStart. ex. Awaiting Vendor",
        default="",
    )
    status_mapping_closed: str = Field(
        title="Closed Status",
        description="Status to transition to in Jira for closed alerts. ex. Resolved",
        default="",
    )


JiraV1Settings = create_settings_model(
    "JiraV1Settings",
    {
        IntegrationActionType.CREATE_EXTERNAL_TICKET: JiraCreateIssueSettings,
        IntegrationActionType.UPDATE_EXTERNAL_TICKET_STATUS: JiraUpdateStatusSettings,
        IntegrationActionType.SEARCH_EXTERNAL_TICKET: JiraSearchBulkIssuesSettings,
    },
)
