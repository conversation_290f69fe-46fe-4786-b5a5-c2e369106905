from apps.connectors.integrations import IntegrationActionType, TemplateVersion
from apps.connectors.integrations.vendors.check_point.check_point_harmony_email.v1 import (
    CheckPointHarmonyEmailV1Settings,
)
from apps.demo.integrations.template import DemoConnectionTemplate, DemoTemplateConfig

from .integration import DemoCheckPointHarmonyEmailV1Integration


class DemoCheckPointHarmonyEmailV1TemplateVersion(TemplateVersion):
    integration = DemoCheckPointHarmonyEmailV1Integration
    id = "v1"
    name = "v1"
    supported_actions = []
    settings_model = CheckPointHarmonyEmailV1Settings
    config_model = DemoTemplateConfig
    connection_model = DemoConnectionTemplate
