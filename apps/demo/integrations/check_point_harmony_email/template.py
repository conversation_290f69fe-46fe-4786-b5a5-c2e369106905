from apps.connectors.integrations.vendors.check_point.check_point_harmony_email import (
    CheckPointHarmonyEmailTemplate,
)
from apps.demo.integrations.template import DemoTemplate

from .v1 import DemoCheckPointHarmonyEmailV1TemplateVersion


class DemoCheckPointHarmonyEmailTemplate(DemoTemplate):
    _replaces_template = CheckPointHarmonyEmailTemplate
    versions = {
        DemoCheckPointHarmonyEmailV1TemplateVersion.id: DemoCheckPointHarmonyEmailV1TemplateVersion(),
    }
